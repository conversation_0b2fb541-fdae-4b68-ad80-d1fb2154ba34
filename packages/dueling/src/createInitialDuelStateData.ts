import { DuelStateData } from "./data/DuelStateData";
import { LANES_COUNT, LocationId, makeAllPossibleLocationIds } from "./data/LocationId";
import { CardInstanceId } from "./data/primitives";

export function createInitialDuelStateData(lanesCount = LANES_COUNT) {
  const data: DuelStateData.FullState = {
    phase: "pre",
    turn: {
      turnNumber: 0,
      playerNumber: 0 as any,
      actionsMade: {},
    },
    players: {
      1: { playerNumber: 1, healthPoints: 0, actionPoints: 0, actionPointsCap: 0 },
      2: { playerNumber: 2, healthPoints: 0, actionPoints: 0, actionPointsCap: 0 },
    },
    cards: {} as { [cardInstanceId: CardInstanceId]: DuelStateData.CardInstance },
    locations: makeAllPossibleLocationIds(lanesCount).reduce((obj, locationId) => {
      obj[locationId] = { cards: [] as CardInstanceId[] };
      return obj;
    }, {} as Record<LocationId, DuelStateData.Location>),
    activeModifiers: [] as DuelStateData.ActiveModifier[],
  };

  return data;
}
