import { DuelStateData } from "../data/DuelStateData";
import { DuelStateFacade } from "./DuelStateFacade";

export function createFacadeGetters(facade: DuelStateFacade) {
  const Util = {
    toLocation(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      if ("location" in from) return from.location;
      if ("id" in from) return facade.locations[from.id];
      throw new Error("Invalid argument");
    },
    toPlayer(from: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player) {
      if ("controller" in from) return from.owner;
      if ("owner" in from) return from.owner;
      if ("playerNumber" in from) return facade.players[from.playerNumber];
      throw new Error("Invalid argument");
    },
  };

  const facadeGetters = {
    get currentPlayer() {
      if (facade.turn.playerNumber == null) return null;
      return facade.players[facade.turn.playerNumber];
    },
    get waitingPlayer() {
      return facadeGetters.currentPlayer?.opponent;
    },
    cardBySlug(slug: string) {
      return Object.values(facade.cards).find(card => card.slug === slug);
    },
    playerUnits(player: DuelStateFacade.Player) {
      return player.fieldsUnit;
    },
    playerTraps(player: DuelStateFacade.Player) {
      return player.fieldsTrap;
    },
    playerOpponent(player: DuelStateFacade.Player) {
      return player.opponent;
    },
    opposingUnitField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.owner.opponent.fieldsUnit[lot.fieldIndex!];
    },
    opposingUnit(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.opposingUnitField(from);
      return field && field.hasCards ? field.cards[0] : null;
    },
    opposingTrapField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.owner.opponent.fieldsTrap[lot.fieldIndex!];
    },
    opposingTrap(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.opposingTrapField(from);
      return field && field.hasCards ? field.cards[0] : undefined;
    },
    sameLaneUnitField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.fieldIndex !== undefined ? lot.owner.fieldsUnit[lot.fieldIndex] : undefined;
    },
    sameLaneUnit(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.sameLaneUnitField(from);
      return field && field.hasCards ? field.cards[0] : undefined;
    },
    sameLaneTrapField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.fieldIndex !== undefined ? lot.owner.fieldsTrap[lot.fieldIndex] : undefined;
    },
    sameLaneTrap(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.sameLaneTrapField(from);
      return field && field.hasCards ? field.cards[0] : undefined;
    },
    adjacentUnitFields(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.sameLaneUnitField(from);
      if (!field || !field.isField) return [];
      return [[1], [0, 2], [1, 3], [2]][field.fieldIndex!].map(i => field.owner.fieldsUnit[i]);
    },
    adjacentUnits(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      return this.adjacentUnitFields(from).filter(field => field.topCard);
    },
    leftUnitField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.fieldIndex! > 0 ? lot.owner.fieldsUnit[lot.fieldIndex! - 1] : undefined;
    },
    leftUnit(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.leftUnitField(from);
      return field && field.hasCards ? field.cards[0] : undefined;
    },
    rightUnitField(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const lot = Util.toLocation(from);
      return lot.fieldIndex! < lot.owner.fieldsUnit.length - 1 ? lot.owner.fieldsUnit[lot.fieldIndex! + 1] : undefined;
    },
    rightUnit(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      const field = this.rightUnitField(from);
      return field && field.hasCards ? field.cards[0] : undefined;
    },

    unitMadeAllActionsThisTurn(card: DuelStateFacade.Card, ...actionTypes: DuelStateData.UnitActionType[]) {
      const cardActions = facade.turn.getUnitActionsMadeThisTurn(card);
      return actionTypes.every(action => cardActions.includes(action));
    },
    unitMadeSomeActionsThisTurn(card: DuelStateFacade.Card, ...actionTypes: DuelStateData.UnitActionType[]) {
      const cardActions = facade.turn.getUnitActionsMadeThisTurn(card);
      return actionTypes.some(action => cardActions.includes(action));
    },
    requiresTribute: (card: DuelStateFacade.Card) => {
      return card.stars > 0;
    },
    acceptsTribute: (card: DuelStateFacade.Card, tribute: DuelStateFacade.Card) => {
      return tribute.stars >= card.stars - 1;
    },
  };

  return facadeGetters;
}

export type DuelStateFacadeGetters = ReturnType<typeof createFacadeGetters>;
