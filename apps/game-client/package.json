{"name": "@drimgar/game-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@babylonjs/core": "^6.49.0", "@clerk/clerk-react": "^5.32.1", "@drimgar/backend": "workspace:*", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.130.2", "convex": "^1.25.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tanstack/router-plugin": "^1.130.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.11.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.38.0", "vite": "^7.0.6"}}