import { DuelStateData } from "../data/DuelStateData";

type FlipEffectBlueprint = {
  readonly type: "flip";
  readonly manner: "combat" | "safe";
  readonly implementation: string;
};

export type CardModelBlueprint = Partial<
  Omit<DuelStateData.CardModel, "effects"> & {
    effects: (DuelStateData.CardEffect | FlipEffectBlueprint)[];
  }
>;

export module CardModelBlueprint {
  export function toData(blueprint: CardModelBlueprint): DuelStateData.CardModel {
    return {
      guid: "placeholder-guid",
      slug: "unknown",
      description: "Unknown",
      ////
      type: DuelStateData.CardType.Unit,
      basePower: 0,
      baseStars: 0,
      keywords: [],
      ...blueprint,
      effects: [
        ...(blueprint.effects?.map<DuelStateData.CardEffect>(effectBlueprint => {
          if (effectBlueprint.type === "flip") {
            const result = {
              type: "active",
              operationType: "UnitReveal",
              operationStatus: "ended",
              locationTypes: ["field"],
              condition: `event.manner === '${effectBlueprint.manner}'`,
              implementation: effectBlueprint.implementation,
            } as DuelStateData.CardEffect;
            return result;
          } else {
            return effectBlueprint;
          }
        }) ?? []),
      ],
    };
  }
}
