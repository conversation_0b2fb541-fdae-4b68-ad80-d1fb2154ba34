import { defineConfig } from 'vite';

// import tanstackRouter from '@tanstack/router-plugin/vite';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react-swc';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    // tanstackRouter({
    //   target: 'react',
    //   autoCodeSplitting: true,
    // }),
    react(),
    tailwindcss(),
  ],
  //// set port to 2025
  server: {
    port: 2025,
  },
});
