---
interface Props {
	title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content="Component Samples" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
	</head>
	<body>
		<slot />
	</body>
</html>
<style is:global>
	:root {
		--accent: 136, 58, 234;
		--accent-light: 224, 204, 250;
		--accent-dark: 49, 10, 101;
	}

	:root[data-theme="light"] {
		--bg-primary: #ffffff;
		--bg-secondary: #f5f5f5;
		--text-primary: #333333;
		--text-secondary: #666666;
		--text-active: #000000;
		--border-color: #e0e0e0;
		--hover-color: #f0f0f0;
		--active-color: #e5e5e5;
		--error-color: #dc3545;
		--error-bg: rgba(220, 53, 69, 0.1);
		--checkerboard-pattern: 
			linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
			linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
			linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
			linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
	}

	:root[data-theme="dark"] {
		--bg-primary: #1e1e1e;
		--bg-secondary: #2a2a2a;
		--text-primary: #ffffff;
		--text-secondary: #aaaaaa;
		--text-active: #ffffff;
		--border-color: #3a3a3a;
		--hover-color: #2a2a2a;
		--active-color: #3a3a3a;
		--error-color: #ff6b6b;
		--error-bg: rgba(255, 107, 107, 0.1);
		--checkerboard-pattern: 
			linear-gradient(45deg, #252525 25%, transparent 25%),
			linear-gradient(-45deg, #252525 25%, transparent 25%),
			linear-gradient(45deg, transparent 75%, #252525 75%),
			linear-gradient(-45deg, transparent 75%, #252525 75%);
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	html, body {
		height: 100%;
		overflow: hidden;
		font-family: system-ui, -apple-system, sans-serif;
		background: var(--bg-primary);
		color: var(--text-primary);
	}

	#root {
		height: 100%;
		width: 100%;
	}

	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	::-webkit-scrollbar-track {
		background: var(--bg-primary);
	}

	::-webkit-scrollbar-thumb {
		background: var(--border-color);
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: var(--text-secondary);
	}
</style>