import { DuelStateData } from "./DuelStateData";
import { LocationId } from "./LocationId";
import { PlayerNumber, CardInstanceId } from "./primitives";

export function createDataGetters($data: DuelStateData.FullState) {
  const context = {};

  function resolvePotentialCallable<T extends {}>(value: T | ((ctx: typeof context) => T)): T {
    return typeof value === "function" ? (value as Function)(context) : value;
  }

  const getters = {
    getLocationById: (locationId: LocationId) => {
      return $data.locations[locationId];
    },
    getPlayerByNumber: (playerNumber: PlayerNumber) => {
      return $data.players[playerNumber];
    },
    getCardByInstanceId: (instanceId: CardInstanceId) => {
      return $data.cards[instanceId] || null;
    },

    getCardsInstanceIdsAtLocation: (locationId: LocationId) => {
      return $data.locations[locationId].cards;
    },
    getCardsAtLocation: (locationId: LocationId) => {
      return $data.locations[locationId].cards.map(instanceId => $data.cards[instanceId]);
    },
    getCardLocation: (instanceId: CardInstanceId) => {
      return $data.cards[instanceId]?.locationId || null;
    },

    getCardPower: makeNonRecursiveFunction(
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        let power = card.model.basePower || 0;
        for (const modifier of $data.activeModifiers) {
          if (modifier.cardInstanceId !== instanceId) continue;
          const offset = modifier.powerOffset === undefined ? 0 : +resolvePotentialCallable(modifier.powerOffset);
          power += offset;
        }
        return power;
      },
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        return 0;
      }
    ),
    getCardStars: makeNonRecursiveFunction(
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        let stars = card.model.baseStars || 0;
        for (const modifier of $data.activeModifiers) {
          if (modifier.cardInstanceId !== instanceId) continue;
          const offset = modifier.starsOffset === undefined ? 0 : +resolvePotentialCallable(modifier.starsOffset);
          stars += offset;
        }
        return stars;
      },
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        return 0;
      }
    ),
    getCardKeywords: makeNonRecursiveFunction(
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        let keywords = [...card.model.keywords];
        for (const modifier of $data.activeModifiers) {
          if (modifier.cardInstanceId !== instanceId) continue;
          if (modifier.keywordsRemoved) keywords = keywords.filter(k => !modifier.keywordsRemoved!.includes(k));
        }
        for (const modifier of $data.activeModifiers) {
          if (modifier.cardInstanceId !== instanceId) continue;
          if (modifier.keywordsAdded) keywords.push(...modifier.keywordsAdded);
        }
        return keywords;
      },
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        return [];
      }
    ),
    getCardFaceUp: makeNonRecursiveFunction(
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        let faceUp = card.faceUp || false;
        for (const modifier of $data.activeModifiers) {
          if (modifier.cardInstanceId !== instanceId) continue;
          if (modifier.faceUp !== undefined) faceUp = modifier.faceUp;
        }
        return faceUp;
      },
      (instanceId: CardInstanceId) => {
        const card = $data.cards[instanceId];
        if (!card) throw new Error(`Card not found: ${instanceId}`);

        return card.faceUp;
      }
    ),
  };

  return getters;
}

function makeNonRecursiveFunction<T extends any[], R extends any>(func1: (...args: T) => R, func2: (...args: T) => R) {
  const recursiveCalls = new Set<string>();

  return function (...args: T) {
    const key = JSON.stringify(args);

    if (recursiveCalls.has(key)) {
      return func2(...args);
    }

    recursiveCalls.add(key);
    const result = func1(...args);
    recursiveCalls.delete(key);

    return result;
  };
}
