import { CardInstanceId, LocationId, PlayerNumber } from "../data/primitives";
import { DuelStateFacade } from "../facade/DuelStateFacade";

export type InstanceToId<T> = //// //// //// ////
  T extends string | number | boolean
    ? T
    : T extends any[]
    ? any[]
    : T extends DuelStateFacade.Location
    ? LocationId
    : T extends DuelStateFacade.Player
    ? PlayerNumber
    : T extends DuelStateFacade.Card
    ? CardInstanceId
    : never;

export type Spread<T extends any[]> = T extends [infer F, ...infer R] ? F[] | Spread<R> : [];

export type IterableInstanceToId<T extends any[]> = Spread<{
  [K in keyof T]: InstanceToId<T[K]>;
}>;
