import { DuelConfiguration, createDuelContext } from "../DuelStateContext";
import { createSubjectiveDuelStateData } from "../SubjectiveDuelStateData";
import { DuelStateData } from "../data/DuelStateData";
import { PlayerNumber } from "../data/primitives";
import { CardModelBlueprint } from "../serialization/CardModelBlueprint";
import { serializeOperationProperties } from "../serialization/serializeOperationProperties";
import { CallbackList } from "../util/CallbackList";
import { createCommandFactory } from "./commands";
import { CardEffects } from "./effects";
import { createEventHistoryController } from "./operation-events";
import { createOperationRunner } from "./operation-runner";
import { Operations } from "./operations";

export function createDuelGameplayController($data: DuelStateData.FullState, duelConfiguration: DuelConfiguration) {
  // const facade = DuelStateFacade.createFromData(data);
  // const mutators = createDataMutators(data);

  const context = createDuelContext($data, duelConfiguration);
  const { facade, mutators } = context;

  const operationRunner = createOperationRunner(context, onOperationStateChange());

  const onOperationStatusChangeHandlers = new CallbackList<(operation: Operations.OperationInstance) => void>();
  const onceRootOperationEndedCallbacks = new CallbackList<() => void>();

  function onOperationStateChange(): (operation: Operations.OperationInstance<any, any>) => void {
    return operation => {
      onOperationStatusChangeHandlers.call(operation);
      if (operation.state.status === "ended") {
        handleOperationEnded(operation);
      }
    };
  }

  function handleOperationEnded<T extends Operations.OperationType>(operation: Operations.OperationInstance<T>) {
    //// Most functionality is handled purely by operational sequences,
    //// but there are some parts of the gamplay loop that we need to handle outside of operation logic,
    //// by reacting to some operations' ended state.

    //// Clean up expired modifiers
    for (let i = facade.activeModifiers.length - 1; i >= 0; i--) {
      const modifier = facade.activeModifiers[i];
      const isExpired = modifier.expiry?.(operation) || false;
      if (!isExpired) continue;
      mutators.removeModifier(modifier);
    }

    function isOperationType<TType extends Operations.OperationType>(
      operation: Operations.OperationInstance<any>,
      type: TType
    ): operation is Operations.OperationInstance<TType> {
      return operation.type === type;
    }

    //// For example, whenever a unit card leaves play, we want to reset its actions history.
    if (isOperationType(operation, "ChangeCardLocation")) {
      const { card } = operation.props;
      const cardWasInPlay = card.prevLocation.isField;
      const cardIsInPlay = card.location.isField;
      const cardLeftPlay = cardWasInPlay && !cardIsInPlay;
      if (cardLeftPlay) {
        mutators.forgetUnitActionsThisTurn(card.instanceId);
      }
    }

    if (isOperationType(operation, "EndTurn") || isOperationType(operation, "StartMatch")) {
      mutators.forgetAllUnitActionsThisTurn();

      const nextTurnNumber = isOperationType(operation, "StartMatch") ? 1 : operation.props.turnNumber + 1;
      const nextTurnPlayer = isOperationType(operation, "StartMatch")
        ? operation.props.firstPlayer
        : operation.props.player.opponent;
      onceRootOperationEndedCallbacks.add(() => {
        runOperation("StartTurn", { player: nextTurnPlayer, turnNumber: nextTurnNumber }, null);
      });
    }

    if (isOperationType(operation, "DeployUnit")) {
      const { card, isManual } = operation.props;
      if (isManual) {
        mutators.rememberUnitActionThisTurn(card.instanceId, DuelStateData.UnitActionType.ManualDeploy);
      }
    }

    if (operation.parent === null) {
      onceRootOperationEndedCallbacks.callAndPurge();
    }
  }

  function runOperation<TType extends Operations.OperationType>(
    operationType: TType,
    props: Operations.OperationPropsPerType[TType],
    parent: Operations.OperationInstance<any, any> | null
  ) {
    const events = createEventHistoryController();

    const removeHandler = onOperationStatusChangeHandlers.add(operation =>
      events.appendEventFromOperationState(operation)
    );

    const operation = operationRunner.runOperation(operationType, props, parent);

    removeHandler();

    const subjectiveStates = {
      [1]: createSubjectiveDuelStateData($data, 1),
      [2]: createSubjectiveDuelStateData($data, 2),
    };

    return { operation, events, subjectiveStates };
  }

  ////

  function prepareBoard(deckOneBlueprints: CardModelBlueprint[], deckTwoBlueprints: CardModelBlueprint[]) {
    const playerOneDeck = deckOneBlueprints.map(blueprint => CardModelBlueprint.toData(blueprint));
    const playerTwoDeck = deckTwoBlueprints.map(blueprint => CardModelBlueprint.toData(blueprint));

    return runOperation("PrepareBoard", { deckOne: playerOneDeck, deckTwo: playerTwoDeck }, null);
  }

  function startMatch(firstPlayerNumber: PlayerNumber) {
    const firstPlayer = facade.players[firstPlayerNumber];

    return runOperation("StartMatch", { firstPlayer }, null);
  }

  const commandFactory = createCommandFactory(context);
  type CommandType = keyof typeof commandFactory;

  function submitCommand<T extends CommandType>(commandType: T, ...args: Parameters<(typeof commandFactory)[T]>) {
    for (const [i, arg] of args.entries()) {
      if (arg === undefined)
        throw new Error(`
          Argument [${i}] is undefined.
          Command: ${commandType}
          Arguments: ${Object.values(serializeOperationProperties(args as any))}
        `);
    }

    const commandFactoryFunction = commandFactory[commandType] as Function;
    const commandFactoryFunctionArgs = args as Parameters<(typeof commandFactory)[T]>;
    const command = commandFactoryFunction(...commandFactoryFunctionArgs);

    return runOperation("HandleCommand", { command }, null);
  }

  ////
  ////
  ////

  const activeEffectsController = CardEffects.createActiveEffectsController(context, operationRunner.runOperation);
  const passiveEffectsController = CardEffects.createPassiveEffectsController(context);

  function clearEphemeralActiveModifiers() {
    for (let i = facade.activeModifiers.length - 1; i >= 0; i--) {
      const modifier = facade.activeModifiers[i];
      if (!modifier.ephemeral) continue;
      mutators.removeModifier(modifier);
    }
  }

  //// Resolve effects
  onOperationStatusChangeHandlers.add(operation => {
    clearEphemeralActiveModifiers();

    //// Resolve passive effects

    for (const card of CardEffects.iterateOverCardsEligibleForEffectActivation(context)) {
      for (const $effect of card.model.effects) {
        if ($effect.type !== "passive") continue;

        const isCardLocationAllowed = CardEffects.isLocationOfType(card.location, $effect.locationTypes);
        if (!isCardLocationAllowed) continue;

        const conditionMet = passiveEffectsController.runConditionScript(card, $effect);
        if (!conditionMet) continue;

        passiveEffectsController.runImplementationScript(card, $effect);
      }
    }

    //// Resolve active effects

    const triggerOperation = operation;
    for (const card of CardEffects.iterateOverCardsEligibleForEffectActivation(context)) {
      for (const $effect of card.model.effects) {
        if ($effect.type !== "active") continue;

        if ($effect.operationType !== triggerOperation.type) continue;
        if ($effect.operationStatus !== triggerOperation.state.status) continue;

        const isCardLocationAllowed = CardEffects.isLocationOfType(card.location, $effect.locationTypes);
        if (!isCardLocationAllowed) continue;

        const conditionMet = activeEffectsController.runConditionScript(card, $effect, triggerOperation);
        if (!conditionMet) continue;

        if (card.isUnit) {
          operationRunner.runOperation("ActivateUnitAbility", { card, effect: $effect, triggerOperation }, operation);
        } else if (card.isTrap) {
          try {
            operationRunner.runOperation("ActivateTrap", { card, effect: $effect, triggerOperation }, operation);
          } catch (error) {
          } finally {
            operationRunner.runOperation("DestroyTrap", { card }, operation);
          }
        }

        // activeEffectsController.runImplementationScript(card, $effect, operation);
      }
    }
  });

  //// //// //// //// ////
  //// //// //// //// ////
  //// //// //// //// ////

  const gameplayController = {
    context,

    //// Initiate the operation chain pertaining to starting a match
    prepareBoard,

    //// Initiate the operation chain pertaining to starting a match
    startMatch,

    //// Initiate the operation chain for resolving a player command
    submitCommand,

    onOperationStatusChangeHandlers,
  };

  return gameplayController;
}

export type DuelGameplayController = ReturnType<typeof createDuelGameplayController>;
