import { DuelStateContext } from "../DuelStateContext";
import { DuelStateData } from "../data/DuelStateData";
import { Keyword } from "../data/primitives";
import { DuelStateFacade } from "../facade/DuelStateFacade";
import { PlayerCommand } from "./commands";
import { CardEffects } from "./effects";

export module Operations {
  export type OperationStatus = "began" | "ended" | "failed";

  export type OperationMethodContext = DuelStateContext & {
    readonly runChildOperation: <K extends OperationType>(
      operationType: K,
      props: OperationPropsPerType[K]
    ) => OperationInstance<K>;
  };

  export class OperationInstance<
    TType extends OperationType = OperationType,
    TParent extends OperationInstance<OperationType, any> | null = any
  > {
    public state = {
      status: "began" as OperationStatus,
      error: null as Error | null,
    };

    constructor(
      public readonly uid: number,
      public readonly parent: TParent,

      public readonly type: TType,
      public readonly props: OperationPropsPerType[TType],
      public readonly apply?: (
        this: OperationInstance<TType, TParent>,
        context: OperationMethodContext,
        props: OperationPropsPerType[TType]
      ) => void,
      public readonly finish?: (
        this: OperationInstance<TType, TParent>,
        context: OperationMethodContext,
        props: OperationPropsPerType[TType]
      ) => void,
      public readonly setup?: (
        this: OperationInstance<TType, TParent>,
        context: OperationMethodContext,
        props: OperationPropsPerType[TType]
      ) => void
    ) {}
  }

  type FlipManner = any;
  type DeathType = "combat" | "effect-kill" | "effect-sacrifice" | "tribute";
  export type OperationPropsPerType = {
    readonly PrepareBoard: { deckOne: DuelStateData.CardModel[]; deckTwo: DuelStateData.CardModel[] };

    readonly StartMatch: { readonly firstPlayer: DuelStateFacade.Player };
    readonly EndMatch: { readonly winner: DuelStateFacade.Player | null };
    readonly StartTurn: {
      readonly player: DuelStateFacade.Player;
      turnNumber: number;
    };
    readonly EndTurn: {
      readonly player: DuelStateFacade.Player;
      readonly isManual: boolean;
      turnNumber: number;
    };

    readonly HandleCommand: { readonly command: PlayerCommand };
    readonly Surrender: {
      readonly player: DuelStateFacade.Player;
    };

    readonly SpawnCard: {
      readonly owner: DuelStateFacade.Player;
      readonly cardModel: DuelStateData.CardModel;
      readonly at: DuelStateFacade.Location;
      readonly faceUp: boolean;
      readonly atIndex: number;
      card?: DuelStateFacade.Card;
    };

    readonly SpawnToken: {
      readonly at: DuelStateFacade.Location;
      readonly slug: string;
      readonly faceUp: boolean;
      card?: DuelStateFacade.Card;
    };

    readonly ChangeCardFaceUp: { readonly card: DuelStateFacade.Card; readonly faceUp: boolean };
    readonly ChangeCardLocation: {
      readonly card: DuelStateFacade.Card;
      readonly to: DuelStateFacade.Location;
      readonly faceUp?: boolean;
      readonly index?: number;
      from?: DuelStateFacade.Location;
    };
    readonly SwapCardLocations: { readonly cardA: DuelStateFacade.Card; readonly cardB: DuelStateFacade.Card };
    readonly ShuffleDeck: {
      readonly player: DuelStateFacade.Player;
      shuffledCards?: DuelStateFacade.Card[];
    };

    readonly AddCardToHand: { readonly card: DuelStateFacade.Card };
    readonly AddCardToDeck: { readonly card: DuelStateFacade.Card };
    readonly AddCardToGrave: { readonly card: DuelStateFacade.Card };

    readonly DiscardCard: { readonly card: DuelStateFacade.Card };
    readonly DestroyUnit: { readonly card: DuelStateFacade.Card; readonly death_type: DeathType };
    readonly DestroyTrap: { readonly card: DuelStateFacade.Card };

    readonly DrawCard: {
      readonly player: DuelStateFacade.Player;
      readonly count: number;
      from?: DuelStateFacade.Location;
    };
    readonly DeployUnit: {
      readonly card: DuelStateFacade.Card;
      readonly to: DuelStateFacade.Location;
      readonly isManual: boolean;
      faceUp?: boolean;
    };
    readonly DeployTrap: {
      readonly card: DuelStateFacade.Card;
      readonly to: DuelStateFacade.Location;
      readonly isManual: boolean;
    };
    readonly DeployUnitWithTribute: {
      readonly card: DuelStateFacade.Card;
      readonly to: DuelStateFacade.Location;
      readonly isManual: boolean;
      tribute?: DuelStateFacade.Card;
    };

    readonly UnitReveal: {
      readonly card: DuelStateFacade.Card;
      readonly manner: FlipManner;
      readonly isManual: boolean;
    };
    readonly UnitRelocate: {
      readonly card: DuelStateFacade.Card;
      readonly to: DuelStateFacade.Location;
      readonly isManual: boolean;
    };
    readonly UnitAttack: {
      readonly card: DuelStateFacade.Card;
      readonly isManual: boolean;
    };
    readonly UnitAttackDirect: { readonly card: DuelStateFacade.Card; retry?: boolean };
    readonly UnitAttackDirectPerform: { readonly card: DuelStateFacade.Card };
    readonly UnitAttackCombat: {
      readonly attacker: DuelStateFacade.Card;
      readonly defender: DuelStateFacade.Card;
      retry?: boolean;
    };
    readonly UnitAttackCombatPerform: {
      readonly attacker: DuelStateFacade.Card;
      readonly defender: DuelStateFacade.Card;
    };

    readonly ActivateTrap: {
      readonly card: DuelStateFacade.Card;
      readonly effect: DuelStateData.CardEffect & { type: "active" };
      readonly triggerOperation: OperationInstance<any>;
    };
    readonly ActivateUnitAbility: {
      readonly card: DuelStateFacade.Card;
      readonly effect: DuelStateData.CardEffect & { type: "active" };
      readonly triggerOperation: OperationInstance<any>;
    };
    readonly PerformActiveEffect: {
      readonly card: DuelStateFacade.Card;
      readonly effect: DuelStateData.CardEffect & { type: "active" };
      readonly triggerOperation: OperationInstance<any>;
    };

    readonly DamagePlayer: { readonly player: DuelStateFacade.Player; readonly amount: number };
    readonly HealPlayer: { readonly player: DuelStateFacade.Player; readonly amount: number };
    readonly ChangePlayerHealthPoints: { readonly player: DuelStateFacade.Player; readonly offset: number };
    readonly ChangePlayerActionPoints: { readonly player: DuelStateFacade.Player; readonly offset: number };
    readonly ChangePlayerActionPointsCap: { readonly player: DuelStateFacade.Player; readonly offset: number };

    readonly DisplayCards: { readonly cards: DuelStateFacade.Card[] };
  };
  export type OperationType = keyof OperationPropsPerType;
  export type OperationProps = OperationPropsPerType[OperationType];

  export type OperationMethodsPerType = {
    [K in OperationType]: Pick<OperationInstance<K, OperationInstance>, "apply" | "finish" | "setup">;
  };
  export const operationMethodsPerType: OperationMethodsPerType = {
    PrepareBoard: {
      apply({ runChildOperation, mutators, facade }, { deckOne, deckTwo }) {
        mutators.changeDuelPhase("pre");

        const preparePlayer = (player: DuelStateFacade.Player, deck: DuelStateData.CardModel[]) => {
          const owner = player;
          const at = player.deck;
          const atIndex = 0;
          const faceUp = false;
          for (const cardModel of deck) {
            runChildOperation("SpawnCard", { owner, cardModel, at, atIndex, faceUp });
          }
        };

        preparePlayer(facade.players[1], deckOne);
        preparePlayer(facade.players[2], deckTwo);
      },
    },

    StartMatch: {
      apply({ runChildOperation, mutators, configuration }, { firstPlayer }) {
        const { settings } = configuration;

        const secondPlayer = firstPlayer.opponent;

        mutators.changeDuelPhase("ongoing");

        mutators.changePlayerHealthPoints(firstPlayer.playerNumber, settings.initialHealthPoints);
        mutators.changePlayerHealthPoints(secondPlayer.playerNumber, settings.initialHealthPoints);

        mutators.changePlayerActionPointsCap(firstPlayer.playerNumber, settings.initialActionPointsFirst);
        mutators.changePlayerActionPointsCap(secondPlayer.playerNumber, settings.initialActionPointsSecond);

        runChildOperation("ShuffleDeck", { player: firstPlayer });
        runChildOperation("ShuffleDeck", { player: secondPlayer });

        runChildOperation("DrawCard", { player: firstPlayer, count: settings.initialHandSizeFirst });
        runChildOperation("DrawCard", { player: secondPlayer, count: settings.initialHandSizeSecond });
      },
    },
    EndMatch: {
      apply({ mutators }, { winner }) {
        mutators.changeDuelPhase("over");

        console.debug(`Winner: #${winner?.playerNumber ?? "none"}`);
      },
    },
    StartTurn: {
      setup({ facade, mutators }, { player }) {
        mutators.incrementTurnNumber();
        mutators.changeTurnPlayer(player.playerNumber);

        this.props.turnNumber = facade.turn.turnNumber;
      },
      apply({ runChildOperation, facade, configuration }, { player }) {
        //// Increment the player's action points cap on turn start
        if (facade.turn.turnNumber > 2) {
          if (player.actionPointsCap < configuration.settings.maxActionPoints) {
            runChildOperation("ChangePlayerActionPointsCap", { player, offset: 1 });
          }
        }

        //// Give the player action points up to their cap to spend during their turn
        if (player.actionPoints < player.actionPointsCap) {
          const actionPointsToGive = player.actionPointsCap - player.actionPoints;
          runChildOperation("ChangePlayerActionPoints", { player, offset: actionPointsToGive });
        }

        //// DrawCard cards on turn start
        if (configuration.features.cardsToDrawOnTurnStart > 0) {
          let cardsToDraw = configuration.features.cardsToDrawOnTurnStart;
          while (cardsToDraw > 0) {
            cardsToDraw--;

            if (player.deck.cardsCount > 0) {
              runChildOperation("DrawCard", { player, count: 1 });
            } else {
              const damageToTake = configuration.features.damageOnTurnStartPerMissingDeckCard;
              if (damageToTake > 0) {
                //// If the player's deck is empty, they take damage for each card they would have drawn
                runChildOperation("DamagePlayer", { player, amount: damageToTake });
              } else {
                break;
              }
            }
          }
        }

        //// Trigger berserk behaviors
        for (const unit of player.units) {
          if (!unit.keywords.includes(Keyword.BERSERK)) continue;
          runChildOperation("UnitAttack", { card: unit, isManual: true });
        }
      },
    },
    EndTurn: {
      apply({ runChildOperation, assert, configuration }, { player }) {
        assert.isCurrentPlayer(player);

        const actionPointsLeftOver = player.actionPoints;
        runChildOperation("ChangePlayerActionPoints", { player, offset: -actionPointsLeftOver });

        if (configuration.features.healPlayerOnTurnEnd) {
          runChildOperation("HealPlayer", { player, amount: actionPointsLeftOver });
        }
      },
      finish({ mutators }) {
        if (this.state.status === "ended") {
          mutators.changeTurnPlayer(null);
        }
      },
    },

    HandleCommand: {
      apply({ runChildOperation, assert }, { command }) {
        assert.hasTurn(command.player);
        assert.canAffordCommand(command);

        if (command.cost > 0) {
          command.assertCommandIsPossible();
          runChildOperation("ChangePlayerActionPoints", { player: command.player, offset: -command.cost });
        }

        //// Ensure the command didn't become impossible while charging the cost
        command.assertCommandIsPossible();

        command.runCommand(runChildOperation);
      },
    },
    Surrender: {
      apply({ runChildOperation }, { player }) {
        runChildOperation("EndMatch", { winner: player.opponent });
      },
    },

    DrawCard: {
      setup(_, props) {
        props.from ??= props.player.deck;
      },
      apply({ runChildOperation }, { from, count }) {
        for (let i = 0; i < count; i++) {
          const topCard = from!.topCard;
          if (!topCard) break;
          runChildOperation("AddCardToHand", { card: topCard });
        }
      },
    },
    DeployUnit: {
      setup(_, props) {
        const { card, isManual } = props;
        props.faceUp ??= isManual && card.hasKeyword(Keyword.SNEAK) ? false : true;
      },
      apply({ runChildOperation, assert }, { card, to, faceUp }) {
        assert.isUnitType(card);
        assert.isUnitField(to);

        assert.custom(!to.hasCards, `Cannot deploy unit to lot ${to} - destination not empty`);

        runChildOperation("ChangeCardLocation", { card, to, faceUp });

        assert.isInPlay(card);
      },
    },
    DeployTrap: {
      apply({ runChildOperation, assert }, { card, to, isManual }) {
        assert.isTrapType(card);
        assert.isTrapField(to);

        if (isManual) {
          assert.canManuallyDeployTo(card, to);
        }

        while (to.topCard) {
          runChildOperation("DestroyTrap", { card: to.topCard });
        }

        runChildOperation("ChangeCardLocation", { card, to, faceUp: false, index: 0 });

        assert.isInPlay(card);
      },
    },
    DeployUnitWithTribute: {
      setup(_, props) {
        props.tribute ??= props.to.topCard!;
      },
      apply({ runChildOperation, assert }, { card, to, tribute, isManual }) {
        assert.isUnitType(card);
        assert.isUnitField(to);

        assert.custom(tribute, `Tribute no longer available`);
        assert.custom(tribute!.isUnit, "Only units can be tributed");
        assert.custom(tribute!.isInPlay, `Tribute no longer in play`);

        runChildOperation("DestroyUnit", { card: tribute!, death_type: "tribute" });

        runChildOperation("DeployUnit", { card, to, isManual, faceUp: true });

        assert.isInPlay(card);
      },
    },

    AddCardToHand: {
      apply({ runChildOperation }, { card }) {
        const destination = card.controller.hand; // card.owner.hand
        const indexAtDestination = destination.cardsCount;
        runChildOperation("ChangeCardLocation", { card, to: destination, faceUp: true, index: indexAtDestination });
      },
    },
    AddCardToDeck: {
      apply({ runChildOperation }, { card }) {
        const destination = card.owner.grave;
        const indexAtDestination = destination.cardsCount;
        runChildOperation("ChangeCardLocation", { card, to: destination, faceUp: true, index: indexAtDestination });
      },
    },
    AddCardToGrave: {
      apply({ runChildOperation }, { card }) {
        const destination = card.owner.grave;
        const indexAtDestination = destination.cardsCount;
        runChildOperation("ChangeCardLocation", { card, to: destination, faceUp: true, index: indexAtDestination });
      },
    },
    DiscardCard: {
      apply({ runChildOperation, assert }, { card }) {
        assert.isInHand(card);
        runChildOperation("AddCardToGrave", { card });
      },
    },
    DestroyUnit: {
      apply({ runChildOperation, assert }, { card }) {
        assert.canDie(card);
        runChildOperation("AddCardToGrave", { card });
      },
    },
    DestroyTrap: {
      apply({ runChildOperation, assert }, { card }) {
        assert.custom(card.isTrap, "Only traps can be disposed");
        assert.custom(card.isInPlay, "Only in-play traps can be disposed");
        runChildOperation("AddCardToGrave", { card });
      },
    },

    SpawnCard: {
      apply({ mutators, facade }, { owner, cardModel, at, faceUp, atIndex }) {
        const caradInstanceId = mutators.createCard(cardModel, at.id, atIndex, faceUp, owner.playerNumber);
        const card = facade.cards[caradInstanceId];
        this.props.card = card;
      },
    },

    SpawnToken: {
      apply({ runChildOperation, library, assert }, { at, slug, faceUp }) {
        if (at.isField) {
          assert.custom(!at.hasCards, `Cannot deploy unit to lot ${at} - it already has cards`);
        }

        const cardModel = library.getBySlug(slug);
        const spawnOp = runChildOperation("SpawnCard", { owner: at.owner, cardModel, at, faceUp, atIndex: 0 });
        this.props.card = spawnOp.props.card;
      },
    },

    ShuffleDeck: {
      apply({ mutators, data, facade, configuration }, { player }) {
        if (configuration.features.skipDeckShuffling) return;

        const deckCards = data.locations[player.deck.id].cards;

        function shuffle<T>(a: T[]) {
          for (let i = a.length - 1, j = 0; i > 0; i--, j = ~~(Math.random() * (i + 1))) [a[i], a[j]] = [a[j], a[i]];
        }

        shuffle(deckCards);
        shuffle(deckCards);
        shuffle(deckCards);

        if (configuration.features.rigDeckShuffling) {
          const copy = [...deckCards];
          const firstCards = [] as typeof copy;

          function pluckCardByCondition(condition: (c: DuelStateFacade.Card) => boolean) {
            const cardInstanceId = copy.find(ciid => condition(facade.cards[ciid]));
            if (cardInstanceId === undefined) return;
            copy.splice(copy.indexOf(cardInstanceId), 1);
            firstCards.push(cardInstanceId);
          }

          pluckCardByCondition(card => card.isUnit && card.stars < 1);
          pluckCardByCondition(card => card.isUnit);
          pluckCardByCondition(card => card.isTrap);

          shuffle(firstCards);

          const topCardIndex = deckCards.length - 1;
          for (const cardInstanceId of firstCards) {
            mutators.setCardIndexAtLocation(cardInstanceId, topCardIndex);
          }
        }

        this.props.shuffledCards = deckCards.map(ciid => facade.cards[ciid]);
      },
    },

    ChangeCardFaceUp: {
      apply({ mutators, assert }, { card, faceUp }) {
        assert.custom(card.isFaceUp !== faceUp, `Card is already ${faceUp ? "face up" : "face down"}`);
        mutators.changeCardFaceUp(card.instanceId, faceUp);
      },
    },
    ChangeCardLocation: {
      setup(_, props) {
        props.from = props.card.location;
      },
      apply({ mutators, assert }, { card, to, from, faceUp, index = to.cardsCount }) {
        assert.custom(card.location !== to, "Card is already in the target location");
        assert.custom(card.location === from, "Card moved from its initial location");

        if (faceUp !== undefined && card.isFaceUp !== faceUp) {
          mutators.changeCardFaceUp(card.instanceId, faceUp);
        }
        mutators.moveCardToLocation(card.instanceId, to.id, index);
        // mutators.changeCardIndexInLot(card.instanceId, index);

        assert.custom(card.location === to, "Card did not reach the target location");
      },
    },
    SwapCardLocations: {
      apply({ runChildOperation }, { cardA, cardB }) {
        const locationA = cardA.location;
        const locationB = cardB.location;
        runChildOperation("ChangeCardLocation", { card: cardA, to: locationB });
        runChildOperation("ChangeCardLocation", { card: cardB, to: locationA });
      },
    },

    UnitReveal: {
      apply({ runChildOperation, assert }, { card, manner }) {
        assert.isInPlay(card);
        assert.isFaceDown(card);
        runChildOperation("ChangeCardFaceUp", { card, faceUp: true });
      },
    },
    UnitRelocate: {
      apply({ runChildOperation, assert }, { card, to, isManual }) {
        assert.isInPlay(card);
        // assert.canRelocateTo(card, to); // ?

        const swappee = to.topCard;
        if (swappee) {
          runChildOperation("SwapCardLocations", { cardA: swappee, cardB: card });
        } else {
          runChildOperation("ChangeCardLocation", { card, to, faceUp: card.isFaceUp, index: 0 });
        }
      },
    },
    UnitAttack: {
      apply({ runChildOperation, getters, assert }, { card }) {
        let retries = 0;
        let retry = false;
        do {
          if (retries > 9) throw new Error("Event retries > 9 !");
          retries++;

          assert.isInPlay(card);
          assert.canAttack(card);

          const direct = check(() => assert.canAttackDirectly(card));
          const e = direct
            ? runChildOperation("UnitAttackDirect", { card })
            : runChildOperation("UnitAttackCombat", { attacker: card, defender: getters.opposingUnit(card)! });
          retry = !!e.props.retry;

          if (!retry) {
            assert.custom(e.state.status === "ended", `Operation ${e.type} failed...`);
          }

          // if (direct) {
          //   const e = runChildOperation("UnitAttackDirect", { card });
          //   retry = !!e.props.retry;
          //   if (!retry) assert.custom(e.state.status === "ended", `${e.type} failed...`);
          // } else {
          //   const opposingUnit = getters.opposingUnit(card);
          //   if (!opposingUnit) throw new Error("No opposing unit");
          //   const e = runChildOperation("UnitAttackCombat", { attacker: card, defender: opposingUnit });
          //   retry = !!e.props.retry;
          //   if (!retry) assert.custom(e.state.status === "ended", `${e.type} failed...`);
          // }
        } while (retry);
      },
    },
    UnitAttackDirect: {
      apply({ runChildOperation, getters, assert }, props) {
        const { card } = props;

        assert.isInPlay(card);
        assert.canAttack(card);

        try {
          assert.canAttackDirectly(card);
        } catch (error) {
          props.retry = true;
          throw error;
        }

        runChildOperation("UnitAttackDirectPerform", { card });

        const opponent = getters.opposingUnitField(card).owner;
        runChildOperation("DamagePlayer", { player: opponent, amount: card.power });
      },
    },
    UnitAttackDirectPerform: {},
    UnitAttackCombat: {
      apply({ runChildOperation, assert }, { attacker, defender }) {
        const opponent = defender.controller;

        const doTheChecks = () => {
          assert.isInPlay(attacker);
          assert.canAttack(attacker);
          try {
            assert.isOpposingUnit(defender, attacker);
          } catch (error) {
            return { retry: true, error };
          }
          return true;
        };
        if (!doTheChecks()) return;

        if (!defender.isFaceUp) {
          runChildOperation("UnitReveal", { card: defender, manner: FlipManner.Combat, isManual: false });
          if (!doTheChecks()) return;
        }

        runChildOperation("UnitAttackCombatPerform", { attacker, defender });

        const powerDelta = attacker.power - defender.power;
        if (powerDelta >= 0) runChildOperation("DestroyUnit", { card: defender, death_type: "combat" });
        if (powerDelta <= 0) runChildOperation("DestroyUnit", { card: attacker, death_type: "combat" });

        if (powerDelta > 0 && attacker.keywords.includes(Keyword.OVERKILL)) {
          runChildOperation("DamagePlayer", { player: opponent, amount: powerDelta });
        }
      },
    },
    UnitAttackCombatPerform: {},

    ActivateTrap: {
      apply(context, { card, effect, triggerOperation }) {
        const { runChildOperation, assert } = context;
        const ctrl = CardEffects.createActiveEffectsController(context, runChildOperation);

        assert.isInPlay(card);
        assert.isFaceDown(card);

        ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);

        if (!card.isFaceUp) {
          runChildOperation("ChangeCardFaceUp", { card, faceUp: true });
          assert.isInPlay(card);
          ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);
        }

        assert.isFaceUp(card);
        ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);

        runChildOperation("PerformActiveEffect", { card, effect, triggerOperation });
      },
    },
    ActivateUnitAbility: {
      apply(context, { card, effect, triggerOperation }) {
        const { runChildOperation, assert } = context;
        const ctrl = CardEffects.createActiveEffectsController(context, runChildOperation);

        ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);

        if (!card.isFaceUp) {
          runChildOperation("ChangeCardFaceUp", { card, faceUp: true });
          ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);
        }

        assert.isFaceUp(card);
        ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);

        runChildOperation("PerformActiveEffect", { card, effect, triggerOperation });

        if (card.isFaceUp && card.location.isHand) {
          runChildOperation("ChangeCardFaceUp", { card, faceUp: false });
        }
      },
    },
    PerformActiveEffect: {
      apply(context, { card, effect, triggerOperation }) {
        const { runChildOperation } = context;
        const ctrl = CardEffects.createActiveEffectsController(context, runChildOperation);

        ctrl.assertActiveEffectIsPossible(card, effect, triggerOperation);

        ctrl.runImplementationScript(card, effect, triggerOperation);
      },
    },

    DamagePlayer: {
      apply({ runChildOperation }, { player, amount }) {
        runChildOperation("ChangePlayerHealthPoints", { player, offset: -amount });
      },
    },
    HealPlayer: {
      apply({ runChildOperation }, { player, amount }) {
        runChildOperation("ChangePlayerHealthPoints", { player, offset: amount });
      },
    },
    ChangePlayerHealthPoints: {
      apply({ mutators }, { player, offset }) {
        mutators.changePlayerHealthPoints(player.playerNumber, player.healthPoints + offset);
      },
    },
    ChangePlayerActionPoints: {
      apply({ mutators }, { player, offset }) {
        mutators.changePlayerActionPoints(player.playerNumber, player.actionPoints + offset);
      },
    },
    ChangePlayerActionPointsCap: {
      apply({ mutators }, { player, offset }) {
        mutators.changePlayerActionPointsCap(player.playerNumber, player.actionPointsCap + offset);
      },
    },

    DisplayCards: {
      apply({ runChildOperation }, { cards }) {
        const faceDownCards = cards.filter(card => !card.isFaceUp);

        for (const card of faceDownCards) {
          runChildOperation("ChangeCardFaceUp", { card, faceUp: true });
        }

        for (const card of faceDownCards) {
          runChildOperation("ChangeCardFaceUp", { card, faceUp: false });
        }
      },
    },
  };

  export function isType<T extends OperationType>(
    operation: OperationInstance<any>,
    type: T
  ): operation is OperationInstance<T> {
    return operation.type === type;
  }
}

function check(assertion: () => unknown): boolean {
  try {
    assertion();
    return true;
  } catch (error) {
    return false;
  }
}

enum FlipManner {
  Combat = "combat",
  Safe = "safe",
}
