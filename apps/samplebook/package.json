{"name": "@example/basics", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/react": "^3.0.10", "@babylonjs/core": "^6.37.1", "@babylonjs/gui": "^6.37.1", "@babylonjs/materials": "^6.37.1", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "astro": "^4.15.3", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}