{"name": "test-in-terminal", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"experiment": "bun --watch main-stateless.ts", "experiment:debug": "bun --inspect-wait=/fooss --watch main-stateless.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@drimgar/dueling": "workspace:*", "@types/node": "^22.10.1", "bun": "^1.0.21", "chalk": "^5.3.0", "readline": "^1.3.0", "typescript": "^5.7.2"}}