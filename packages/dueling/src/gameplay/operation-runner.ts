import { Operations } from "./operations";

import type { DuelStateContext } from "../DuelStateContext";

export function createOperationRunner(
  duelContext: DuelStateContext,
  onOperationStatusChange: (operation: Operations.OperationInstance<any, any>) => void
) {
  let lastOperationUid = 0;

  function runOperation<TType extends Operations.OperationType>(
    operationType: TType,
    props: Operations.OperationPropsPerType[TType],
    parent: Operations.OperationInstance<any, any> | null = null
  ): Operations.OperationInstance<TType> {
    const operationMethods = Operations.operationMethodsPerType[operationType] as any;
    const operationInstance = new Operations.OperationInstance(
      ++lastOperationUid,
      parent,
      operationType,
      props,
      operationMethods.apply,
      operationMethods.finish,
      operationMethods.setup
    );
    const operationContext = {
      ...duelContext,
      runChildOperation: (
        operationType: Operations.OperationType,
        props: Operations.OperationPropsPerType[Operations.OperationType]
      ) => runOperation(operationType, props, operationInstance) as any,
    };

    try {
      operationInstance.setup?.(operationContext, props);

      operationInstance.state.status = "began";
      onOperationStatusChange(operationInstance);

      operationInstance.apply?.(operationContext, props);

      operationInstance.state.status = "ended";
      operationInstance.finish?.(operationContext, props);

      onOperationStatusChange(operationInstance);
    } catch (error: any) {
      operationInstance.finish?.(operationContext, props);

      operationInstance.state.status = "failed";
      operationInstance.state.error = error;
      onOperationStatusChange(operationInstance);
    }

    return operationInstance;
  }

  return { runOperation };
}

export type OperationRunner = ReturnType<typeof createOperationRunner>;
