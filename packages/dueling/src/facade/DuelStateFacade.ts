import { DuelStateData } from "../data/DuelStateData";
import { LocationId, makeAllPossibleLocationIds, LANES_COUNT } from "../data/LocationId";
import { createDataGetters } from "../data/createDataGetters";
import { PlayerNumber, CardInstanceId, Keyword } from "../data/primitives";

export module DuelStateFacade {
  type DuelStateDataGetters = ReturnType<typeof createDataGetters>;
  type DuelStateFacadeGetters = {
    getLocationById(locationId: LocationId): Location;
    getPlayerByNumber(playerNumber: PlayerNumber): Player;
    getCardByInstanceId(instanceId: CardInstanceId): Card;
  };

  export class Location {
    constructor(
      protected readonly dataGetters: DuelStateDataGetters,
      protected readonly facadeGetters: DuelStateFacadeGetters,
      public readonly id: LocationId
    ) {}

    get fieldNumber() {
      if (!this.isField) return undefined;
      return Number(this.id[this.id.length - 1]);
    }

    get fieldIndex() {
      if (!this.isField) return undefined;
      return Number(this.id[this.id.length - 1]) - 1;
    }
    get cards() {
      const $cards = this.dataGetters.getCardsAtLocation(this.id);
      return $cards.map($card => this.facadeGetters.getCardByInstanceId($card.instanceId));
    }
    get cardsCount() {
      const $cards = this.dataGetters.getCardsAtLocation(this.id);
      return $cards.length;
    }
    get hasCards() {
      const $cards = this.dataGetters.getCardsAtLocation(this.id);
      return $cards.length > 0;
    }
    get topCard() {
      const $cardInstanceIds = this.dataGetters.getCardsInstanceIdsAtLocation(this.id);

      if ($cardInstanceIds.length < 1) {
        return null;
      }

      const $topCardInstanceId = $cardInstanceIds[$cardInstanceIds.length - 1];
      return this.facadeGetters.getCardByInstanceId($topCardInstanceId);
    }
    get owner() {
      const playerNumber = Number(this.id[0]) as PlayerNumber;
      return this.facadeGetters.getPlayerByNumber(playerNumber);
    }

    get isHand() {
      return /hnd$/.test(this.id);
    }
    get isDeck() {
      return /dck$/.test(this.id);
    }
    get isGrave() {
      return /grv$/.test(this.id);
    }
    get isUnitField() {
      return /-fu\d$/.test(this.id);
    }
    get isTrapField() {
      return /-ft\d$/.test(this.id);
    }
    get isField() {
      return this.isUnitField || this.isTrapField;
    }

    toString() {
      return `Location(${this.id})`;
    }
  }

  export class Player {
    constructor(
      protected readonly dataGetters: DuelStateDataGetters,
      protected readonly facadeGetters: DuelStateFacadeGetters,
      public readonly playerNumber: PlayerNumber
    ) {}

    get healthPoints() {
      const $player = this.dataGetters.getPlayerByNumber(this.playerNumber);
      return $player.healthPoints;
    }
    get actionPoints() {
      const $player = this.dataGetters.getPlayerByNumber(this.playerNumber);
      return $player.actionPoints;
    }
    get actionPointsCap() {
      const $player = this.dataGetters.getPlayerByNumber(this.playerNumber);
      return $player.actionPointsCap;
    }

    get opponent() {
      return this.playerNumber === 1
        ? this.facadeGetters.getPlayerByNumber(2)
        : this.facadeGetters.getPlayerByNumber(1);
    }

    get hand() {
      return this.facadeGetters.getLocationById(`${this.playerNumber}-hnd`);
    }
    get deck() {
      return this.facadeGetters.getLocationById(`${this.playerNumber}-dck`);
    }
    get grave() {
      return this.facadeGetters.getLocationById(`${this.playerNumber}-grv`);
    }
    get abyss() {
      return this.facadeGetters.getLocationById(`${this.playerNumber}-bys`);
    }
    get locations() {
      const allLocationIds = makeAllPossibleLocationIds(LANES_COUNT);
      const myLocationIds = allLocationIds.filter(locationId =>
        LocationId.isOwnedByPlayerNum(locationId, this.playerNumber)
      );
      return myLocationIds.map(this.facadeGetters.getLocationById);
    }
    get fields() {
      return this.locations.filter(location => location.isField);
    }
    get fieldsUnit() {
      return this.fields.filter(location => location.isUnitField);
    }
    get fieldsTrap() {
      return this.fields.filter(location => location.isTrapField);
    }

    get units() {
      return this.fieldsUnit.map(f => f.topCard!).filter(Boolean);
    }
    get traps() {
      return this.fieldsTrap.map(f => f.topCard!).filter(Boolean);
    }

    toString() {
      return `Player(${this.playerNumber})`;
    }
  }

  export class Card {
    constructor(
      protected readonly dataGetters: DuelStateDataGetters,
      protected readonly facadeGetters: DuelStateFacadeGetters,
      public readonly instanceId: CardInstanceId
    ) {}

    private get $data() {
      return this.dataGetters.getCardByInstanceId(this.instanceId);
    }

    get model() {
      return this.$data.model;
    }
    get isUnit() {
      return this.model.type === DuelStateData.CardType.Unit;
    }
    get isTrap() {
      return this.model.type === DuelStateData.CardType.Trap;
    }

    get slug() {
      return this.model.slug;
    }
    get location() {
      const locationId = this.$data.locationId;
      return this.facadeGetters.getLocationById(locationId);
    }
    get prevLocation() {
      const locationId = this.$data.prevLocationId;
      return this.facadeGetters.getLocationById(locationId);
    }
    get owner() {
      const playerNum = this.$data.ownerPlayerNum;
      return this.facadeGetters.getPlayerByNumber(playerNum);
    }
    get controller() {
      return this.location.owner;
    }
    get isInPlay() {
      return this.location.isField;
    }

    get power() {
      return this.dataGetters.getCardPower(this.instanceId);
    }
    get stars() {
      return this.dataGetters.getCardStars(this.instanceId);
    }
    get keywords() {
      return this.dataGetters.getCardKeywords(this.instanceId);
    }

    get isFaceUp() {
      return this.dataGetters.getCardFaceUp(this.instanceId);
    }
    get isFaceDown() {
      return !this.dataGetters.getCardFaceUp(this.instanceId);
    }

    hasKeyword(keyword: Keyword) {
      return this.keywords.includes(keyword);
    }

    toString() {
      return `Card(${this.instanceId})`;
    }
  }

  export function createFromData(data: DuelStateData.FullState) {
    const dataGetters = createDataGetters(data);
    const facadeGetters = {
      getLocationById(locationId: LocationId): Location {
        return facade.locations[locationId];
      },
      getPlayerByNumber(playerNumber: PlayerNumber): Player {
        return facade.players[playerNumber];
      },
      getCardByInstanceId(instanceId: CardInstanceId): Card {
        return facade.cards[instanceId];
      },
    };

    // type Modifier = Partial<Omit<DuelStateData.ActiveModifier, "cardInstanceId">>;
    const locationFacades: { [locationId: string]: Location } = {};
    for (const key in data.locations) {
      const locationId = key as LocationId;
      locationFacades[locationId] = new Location(dataGetters, facadeGetters, locationId);
    }

    const playerOne = new Player(dataGetters, facadeGetters, 1);
    const playerTwo = new Player(dataGetters, facadeGetters, 2);
    const playerFacades = {
      [1]: playerOne,
      [2]: playerTwo,
    };

    const cardFacadesMap: { [cardInstanceId: number]: Card } = {};
    const cardFacades = new Proxy(data.cards as unknown as typeof cardFacadesMap, {
      get(_, prop) {
        const cardInstanceId = Number(prop) as CardInstanceId;
        if (isNaN(cardInstanceId)) return undefined;
        if (cardInstanceId < 1) return undefined;
        if (!cardFacadesMap[cardInstanceId]) {
          cardFacadesMap[cardInstanceId] = new Card(dataGetters, facadeGetters, cardInstanceId);
        }
        return cardFacadesMap[cardInstanceId];
      },
    });

    const turnFacade = {
      get turnNumber() {
        return data.turn.turnNumber;
      },
      get playerNumber() {
        return data.turn.playerNumber;
      },
      get player(): Player | null {
        if (data.turn.playerNumber == null) return null;
        return facade.players[data.turn.playerNumber];
      },
      getUnitActionsMadeThisTurn(unit: Card) {
        return data.turn.actionsMade[unit.instanceId] || [];
      },
    };

    ////
    const facade = {
      locations: locationFacades,
      players: playerFacades,
      cards: cardFacades,
      get activeModifiers() {
        return data.activeModifiers;
      },
      turn: turnFacade,
    } as const;

    return facade;
  }
}

export type DuelStateFacade = ReturnType<typeof DuelStateFacade.createFromData>;
