{"name": "drimgar-game", "version": "0.0.0", "scripts": {"dev:term": "pnpm --filter test-in-terminal experiment", "dev:client": "pnpm --filter @drimgar/game-client dev", "dev:backend": "pnpm --filter @drimgar/backend dev", "clean": "rm -rf node_modules packages/**/node_modules apps/**/node_modules pnpm-lock.yaml", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore"}}