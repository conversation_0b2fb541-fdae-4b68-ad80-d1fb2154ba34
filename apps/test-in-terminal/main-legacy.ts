import {
  DuelConfiguration,
  DuelStateContext,
  createDuelContext,
} from '@drimgar/dueling/src/DuelStateContext';
import { createInitialDuelStateData } from '@drimgar/dueling/src/createInitialDuelStateData';
import { DuelStateData } from '@drimgar/dueling/src/data/DuelStateData';
import { Keyword, LocationId } from '@drimgar/dueling/src/data/primitives';
import { createDuelGameplayController } from '@drimgar/dueling/src/gameplay/gameplay';
import { createEventHistoryController } from '@drimgar/dueling/src/gameplay/operation-events';

import { DuelPrinter } from './lib/printer';
import { CardModelBlueprint } from '@drimgar/dueling/src/serialization/CardModelBlueprint';

let DRAW_ON_SCREEN = true;
// DRAW_ON_SCREEN = false;

const duelConfiguration: DuelConfiguration = {
  settings: {
    initialActionPointsFirst: 5,
    initialActionPointsSecond: 5,
  },
  features: {
    skipDeckShuffling: true,
  },
};

const printerSettings: Parameters<typeof DuelPrinter.printDuelStateOnScreen>[1] = {
  // hideFaceDownCards: false,
  // condensedDeck: false,
};

function attachPrinter(
  data: DuelStateData.FullState,
  gameplay: ReturnType<typeof createDuelGameplayController>,
  getDelayMs: () => number
) {
  const lastErrors = [] as Error[];
  function handleOperationError(error: Error) {
    lastErrors.push(error);
    console.error(error);
  }

  gameplay.onOperationStatusChangeHandlers.add(operation => {
    if (operation.state.error) handleOperationError(operation.state.error);
  });

  const debugLines = [] as string[];
  console.debug = (...args: any[]) => debugLines.push(args.join(' '));

  const events = createEventHistoryController();
  gameplay.onOperationStatusChangeHandlers.add(operation => {
    if (events.getEventHistory().length > 0) {
      if (operation.parent === null && operation.state.status === 'began') {
        events.appendNull();
      }
    }
    events.appendEventFromOperationState(operation);
  });

  //// Print to console

  function redrawScreen() {
    console.clear();
    DuelPrinter.printDuelStateOnScreen(data, printerSettings);
    console.log();
    // console.log(context.data.activeModifiers);
    // console.log(context.data.turn.actionsMade);
    console.log();
    DuelPrinter.printErrorsOnScreen(lastErrors);
    console.log();
    if (debugLines.length) console.log(debugLines.join('\n'));
    else DuelPrinter.printDuelEventsOnScreen(events);
    console.log();
  }

  gameplay.onOperationStatusChangeHandlers.add(() => {
    if (DRAW_ON_SCREEN) redrawScreen();
    DuelPrinter.pause(getDelayMs());
  });

  return {
    events,
  };
}

function populateDecks({ facade, mutators }: DuelStateContext) {
  function createCard(modelBlueprint: CardModelBlueprint, lotId: LocationId) {
    const modelData = CardModelBlueprint.toData(modelBlueprint);
    const ciid = mutators.createCard(modelData, lotId);
    const card = facade.cards[ciid];
    if (!card) throw new Error('Card not found');
    return card;
  }

  const cards = [
    createCard(
      {
        type: DuelStateData.CardType.Trap,
        effects: [
          {
            type: 'active',
            operationType: 'DeployUnit',
            operationStatus: 'ended',
            locationTypes: ['field'],
            condition: `event.card.instanceId === get.opposingUnit(card)?.instanceId`,
            implementation: `op.kill(event.card)`,
          },
        ],
      },
      '1-dck'
    ),
    createCard(
      {
        basePower: 0,
        effects: [
          {
            type: 'passive',
            locationTypes: ['field', 'grave', 'hand'],
            condition: `true`,
            implementation: `pass.raisePower(card, card.controller.opponent.hand.cardsCount)`,
          },
        ],
      },
      '1-dck'
    ),
    createCard({ basePower: 2 }, '1-dck'),
    createCard({ basePower: 3 }, '1-dck'),
    createCard({ basePower: 4 }, '1-dck'),
    createCard({ basePower: 5 }, '1-dck'),
    createCard({ basePower: 6 }, '1-dck'),
    createCard({ basePower: 7 }, '1-dck'),
    createCard({ basePower: 8 }, '1-dck'),
    createCard({ basePower: 9 }, '1-dck'),

    createCard({ basePower: 1 }, '2-dck'),
    createCard(
      {
        basePower: 2,
        effects: [
          {
            type: 'flip',
            manner: 'combat',
            implementation: `op.kill(get.opposingUnit(card))`,
          },
        ],
        keywords: [Keyword.SNEAK],
      },
      '2-dck'
    ),
    createCard({ basePower: 3 }, '2-dck'),
    createCard({ basePower: 4 }, '2-dck'),
    createCard({ basePower: 5 }, '2-dck'),
    createCard({ basePower: 6 }, '2-dck'),
    createCard({ basePower: 7 }, '2-dck'),
    createCard({ basePower: 8 }, '2-dck'),
    createCard({ basePower: 9 }, '2-dck'),
  ];

  return cards;
}

async function runExperiment0(
  context: DuelStateContext,
  gameplay: ReturnType<typeof createDuelGameplayController>
) {
  const { data } = context;

  async function doEndTurnLoop() {
    while (true) {
      await DuelPrinter.pauseUntilKeypress();
      gameplay.submitCommand('endturn', data.turn.playerNumber!);
    }
  }

  await doEndTurnLoop();
}

async function runExperiment1(
  context: DuelStateContext,
  gameplay: ReturnType<typeof createDuelGameplayController>
) {
  const { facade } = context;
  const p1 = facade.players[1];
  const p2 = facade.players[2];
  const cTrap = p1.hand.cards[0];
  const cAtak = p1.hand.cards[1];

  // gameplay.submitCommand("draw", 1);
  // gameplay.submitCommand("deploy", 1, p1.hand.cards[3], "1-fu2");
  // await DuelPrinter.pauseUntilKeypress();

  gameplay.submitCommand('deploy', 1, cAtak.instanceId, '1-fu2');
  // gameplay.submitCommand("deploy", 1, cTrap, "1-fu1"); // Error: Destination must be a trap-field.
  gameplay.submitCommand('deploy', 1, cTrap.instanceId, '1-ft1');

  // await DuelPrinter.pauseUntilKeypress();

  // gameplay.submitCommand("attack", 1, cAtak); // Error: Unit already performed action(s) of type deploy
  // gameplay.submitCommand("draw", 1); // Error: Not enough action points
  gameplay.submitCommand('endturn', 1);

  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu1');

  // await DuelPrinter.pauseUntilKeypress();

  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu2');
  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu3');
  // gameplay.submitCommand("deploy", 2, p2.hand.cards[0], "2-fu4");
  // gameplay.submitCommand("deploy", 2, p2.hand.cards[0], "2-fu5");
  gameplay.submitCommand('endturn', 2);
  gameplay.submitCommand('attack', 1, cAtak.instanceId);

  // await doEndTurnLoop();

  // await DuelPrinter.pauseUntilKeypress();
  // delayBetweenEventsMs = 200;
  // gameplay.submitCommand("draw", 2);
  // gameplay.submitCommand("draw", 2);
}

async function runExperiment2(
  context: DuelStateContext,
  gameplay: ReturnType<typeof createDuelGameplayController>
) {
  const { facade } = context;
  const p1 = facade.players[1];
  const p2 = facade.players[2];

  gameplay.submitCommand('draw', 1);

  const cAtk = p1.hand.cards[3];
  const cDef = p2.hand.cards[1];

  // console.debug(JSON.stringify(cDef.model, null, 2));

  gameplay.submitCommand('deploy', 1, cAtk.instanceId, '1-fu2');
  gameplay.submitCommand('endturn', 1);

  gameplay.submitCommand('deploy', 2, cDef.instanceId, '2-fu2');
  gameplay.submitCommand('endturn', 2);

  await DuelPrinter.pauseUntilKeypress();

  // events.clear();

  gameplay.submitCommand('attack', 1, cAtk.instanceId); // Error: Unit already performed action(s) of type deploy

  await DuelPrinter.pauseUntilKeypress();

  // gameplay.submitCommand("deploy", 1, cAtak, "1-fu2");
  // gameplay.submitCommand("deploy", 1, cTrap, "1-fu1"); // Error: Destination must be a trap-field.
  // gameplay.submitCommand("deploy", 2, cTrap, "1-ft1");

  // await DuelPrinter.pauseUntilKeypress();

  // gameplay.submitCommand("draw", 1); // Error: Not enough action points

  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu1');

  // await DuelPrinter.pauseUntilKeypress();

  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu2');
  gameplay.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu3');
  // gameplay.submitCommand("deploy", 2, p2.hand.cards[0], "2-fu4");
  // gameplay.submitCommand("deploy", 2, p2.hand.cards[0], "2-fu5");
  // gameplay.submitCommand("attack", 1, cAtak);

  // await doEndTurnLoop();

  // await DuelPrinter.pauseUntilKeypress();
  // delayBetweenEventsMs = 200;
  // gameplay.submitCommand("draw", 2);
  // gameplay.submitCommand("draw", 2);
}

async function runExperiment3(
  context: DuelStateContext,
  gameplay: ReturnType<typeof createDuelGameplayController>
) {}

async function go() {
  while (true) {
    let delayBetweenEventsMs = 8;

    console.clear();

    const data = createInitialDuelStateData();
    const context = createDuelContext(data, duelConfiguration);

    populateDecks(context);

    const gameplay = createDuelGameplayController(data, duelConfiguration);

    const printer = attachPrinter(data, gameplay, () => delayBetweenEventsMs);

    gameplay.startMatch(1);

    ////    //// //// ////
    //// ////    //// //// ////
    //// //// ////    //// //// ////

    // await runExperiment0(context, gameplay);
    // await runExperiment1(context, gameplay);
    await runExperiment2(context, gameplay);
    // await runExperiment3(context, gameplay);
    // await runExperiment4(context, gameplay);
    // await runExperiment5(context, gameplay);

    //// //// ////    //// //// ////
    //// ////    //// //// ////
    ////    //// ////

    const key = await DuelPrinter.pauseUntilKeypress();
    if (key === 'q') break;
  }
}

go();
