import { createDuelContext } from "../DuelStateContext";
import { DuelStateFacade } from "../facade/DuelStateFacade";
import { createInitialDuelStateData } from "../createInitialDuelStateData";

import type { LocationId } from "../data/primitives";
import type { DuelStateData } from "../data/DuelStateData";
import { Operations } from "../gameplay/operations";

describe("Dueling", () => {
  it("test setup must run without errors", () => {});
});
