import { Dueling } from "@drimgar/dueling-legacy";

import { DuelStateData } from "./data/DuelStateData";
import { createDataGetters } from "./data/createDataGetters";
import { CardInstanceId, LocationId, PlayerNumber } from "./data/primitives";
import { DuelStateFacade } from "./facade/DuelStateFacade";
import { createFacadeFAQ } from "./facade/createFacadeFAQ";

export type $DuelOperationResultData = ReturnType<Dueling.Serializer["$$ini"] & Dueling.Serializer["$$go"]>;
export type $SubjectivDuelOperationResultData = NonNullable<$DuelOperationResultData[number]>;
export type $SubjectiveDuelStaticData = $SubjectivDuelOperationResultData["static"];
export type $SubjectiveDuelStateData = $SubjectivDuelOperationResultData["state"];
export type $SerializedKnownCardState = Partial<Exclude<$SubjectiveDuelStateData["cards"][number], number>>;

export function createSubjectiveDuelStateData(
  $data: DuelStateData.FullState,
  playerNum: PlayerNumber
): $SubjectiveDuelStateData {
  const facade = DuelStateFacade.createFromData($data);
  const getters = createDataGetters($data);
  const faq = createFacadeFAQ(facade);

  const statusPhase = {
    pre: "prep" as const,
    ongoing: "started" as const,
    over: "ended" as const,
  }[$data.phase];

  const lotCards = Object.entries($data.locations).reduce((acc, [key, location]) => {
    const lotId = key as LocationId;
    if (location.cards?.length > 0) {
      acc[lotId] = location.cards;
    }
    return acc;
  }, {} as Partial<Record<LocationId, CardInstanceId[]>>);

  const cards: $SubjectiveDuelStateData["cards"][number][] = Object.values($data.cards).map($card => {
    const card = facade.cards[$card.instanceId];

    const canSee = card.isFaceUp || (card.controller.playerNumber === playerNum && !card.location!.isDeck);
    if (!canSee) return 0 as const;

    return {
      power: getters.getCardPower($card.instanceId),
      faceup: getters.getCardFaceUp($card.instanceId),
      tags: getters.getCardKeywords($card.instanceId),
      fresh:
        $data.turn.actionsMade[$card.instanceId]?.some(
          action => action === DuelStateData.UnitActionType.ManualDeploy
        ) || undefined,
      canrev: faq.canManuallyReveal(card) || undefined,
      canatk: faq.canManuallyAttack(card) || undefined,
      canrel: card.controller.fields.filter(field => faq.canManuallyRelocateTo(card, field)).map(field => field.id),
      candep: card.controller.fields.filter(field => faq.canManuallyDeployTo(card, field)).map(field => field.id),
    };
  });

  return {
    turn: {
      player: $data.turn.playerNumber!,
    },
    statusPhase: statusPhase,
    players: [
      {
        hp: $data.players[1].healthPoints,
        ap: $data.players[1].actionPoints,
        // apCap: data.players[1].actionPointsCap,
      },
      {
        hp: $data.players[2].healthPoints,
        ap: $data.players[2].actionPoints,
        // apCap: data.players[2].actionPointsCap,
      },
    ],
    lotCards: lotCards,
    cards: [0, ...cards],
  };
}

export type SubjectiveDuelStateData = ReturnType<typeof createSubjectiveDuelStateData>;
