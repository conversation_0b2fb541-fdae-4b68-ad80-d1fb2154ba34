{"name": "@drimgar/dueling", "version": "0.0.0", "main": "./index.ts", "types": "./index.ts", "private": true, "scripts": {"console": "ts-node --require ./index.ts", "test": "jest --config=jest.config.js", "test:watch": "jest --config=jest.config.js --watch"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.16.5", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2"}, "dependencies": {"bun": "^1.2.19", "chalk": "^5.4.1", "telejson": "^7.2.0"}}