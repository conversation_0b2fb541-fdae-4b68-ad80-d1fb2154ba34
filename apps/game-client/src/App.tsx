import { useQuery, useMutation } from "convex/react";
import { api } from "@drimgar/backend/convex/_generated/api";
import { SignInButton, SignOutButton, useUser } from "@clerk/clerk-react";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useEffect } from "react";

function App() {
  const { user: clerkUser } = useUser();
  const user = useQuery(api.users.getCurrentUser);
  const ensureUser = useMutation(api.users.ensureUser);

  // Ensure user record exists when authenticated
  useEffect(() => {
    if (clerkUser && user === null) {
      // User is authenticated but no record exists, create one
      ensureUser();
    }
  }, [clerkUser, user, ensureUser]);

  return (
    <>
      <AuthLoading>
        <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-gray-300">Loading...</p>
          </div>
        </div>
      </AuthLoading>
      
      <Unauthenticated>
        <div className="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-8">
          <div className="max-w-md w-full bg-gray-800 rounded-lg p-8 shadow-xl">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-2">🎮 Drimgar Game</h1>
              <p className="text-gray-400">Sign in to start your adventure</p>
            </div>
            
            <SignInButton mode="modal">
              <button className="w-full flex items-center justify-center gap-3 bg-white text-gray-900 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200">
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Continue with Google
              </button>
            </SignInButton>
            
            <p className="text-xs text-gray-500 text-center mt-6">
              By continuing, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </Unauthenticated>
      
      <Authenticated>
        <div className="min-h-screen bg-gray-900 text-white">
          {/* Header */}
          <header className="bg-gray-800 border-b border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <h1 className="text-xl font-bold">🎮 Drimgar Game</h1>
                </div>
                
                <div className="flex items-center gap-4">
                  {user && (
                    <div className="flex items-center gap-3">
                      {user.image && (
                        <img
                          src={user.image}
                          alt={user.name}
                          className="w-8 h-8 rounded-full"
                        />
                      )}
                      <span className="text-sm text-gray-300">{user.name}</span>
                    </div>
                  )}
                  
                  <SignOutButton>
                    <button className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                      Sign Out
                    </button>
                  </SignOutButton>
                </div>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-gray-800 rounded-lg p-8 shadow-xl">
              <h2 className="text-2xl font-bold mb-6">Welcome to Drimgar!</h2>
              
              {user && (
                <div className="bg-gray-700 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold mb-4">Your Profile</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="text-gray-400">Name:</span> {user.name}</p>
                    <p><span className="text-gray-400">Email:</span> {user.email}</p>
                    {user.emailVerified && (
                      <p><span className="text-gray-400">Email Verified:</span> ✅</p>
                    )}
                  </div>
                </div>
              )}
              
              <div className="text-center">
                <p className="text-gray-400 mb-4">Game content will appear here...</p>
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg">
                  <span>🚀</span>
                  <span>Ready to play!</span>
                </div>
              </div>
            </div>
          </main>
        </div>
      </Authenticated>
    </>
  );
}

export default App;
