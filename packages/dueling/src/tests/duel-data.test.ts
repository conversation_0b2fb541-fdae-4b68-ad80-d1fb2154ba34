import { makeAllPossibleLocationIds } from "../data/LocationId";
import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";
import { Keyword, LocationId } from "../data/primitives";
import { createDataGetters } from "../data/createDataGetters";
import { CardModelBlueprint } from "../serialization/CardModelBlueprint";

function makeNewDuelContext() {
  const data = createInitialDuelStateData();
  const context = createDuelContext(data);
  const dataGetters = createDataGetters(data);

  function createCard(modelBlueprint: CardModelBlueprint, lotId: LocationId) {
    const modelData = CardModelBlueprint.toData(modelBlueprint);
    const ciid = context.mutators.createCard(modelData, lotId);
    return ciid;

    // const card = context.facade.cards[ciid];
    // if (!card) throw new Error("Card not found");
    // return card;
  }

  return [context, dataGetters, createCard] as const;
}

describe("Duel state data", () => {
  const ALL_LOCATIONS = makeAllPossibleLocationIds(5);

  it("makeAllPossibleLocationIds() should return the correct location ids", () => {
    expect(ALL_LOCATIONS).toEqual([
      "1-hnd",
      "1-dck",
      "1-grv",
      "1-bys",
      "1-fu1",
      "1-fu2",
      "1-fu3",
      "1-fu4",
      "1-fu5",
      "1-ft1",
      "1-ft2",
      "1-ft3",
      "1-ft4",
      "1-ft5",
      "2-hnd",
      "2-dck",
      "2-grv",
      "2-bys",
      "2-fu1",
      "2-fu2",
      "2-fu3",
      "2-fu4",
      "2-fu5",
      "2-ft1",
      "2-ft2",
      "2-ft3",
      "2-ft4",
      "2-ft5",
    ]);
  });

  it("createCard() should create a card with the correct properties", () => {
    const [, dataGetters, createCard] = makeNewDuelContext();

    createCard({ basePower: 5 }, "1-hnd");
    expect(dataGetters.getCardsAtLocation("1-hnd").length).toBe(1);
    expect(dataGetters.getCardsAtLocation("1-hnd")[0].instanceId).toBe(1);
    expect(dataGetters.getCardsAtLocation("1-hnd")[0].model.basePower).toBe(5);
    expect(dataGetters.getCardsAtLocation("1-hnd")[0].locationId).toBe("1-hnd");
  });

  it("getLocationById() should return the correct location", () => {
    const [{ data }, dataGetters] = makeNewDuelContext();

    const locationId = "1-hnd";
    const location = dataGetters.getLocationById(locationId);
    expect(location).toEqual(data.locations[locationId]);
  });

  it("getPlayerByNumber() should return the correct player", () => {
    const [{ data }, dataGetters] = makeNewDuelContext();

    const playerNumber = 1;
    const player = dataGetters.getPlayerByNumber(playerNumber);
    expect(player).toEqual(data.players[playerNumber]);
  });

  it("getCardByInstanceId() should return the correct card", () => {
    const [{ data }, dataGetters, createCard] = makeNewDuelContext();

    const instanceId = createCard({ basePower: 5 }, "1-hnd");
    const card = dataGetters.getCardByInstanceId(instanceId);
    expect(card).toEqual(data.cards[instanceId]);
  });

  it("getCardsInstanceIdsAtLocation() should return the correct card instance ids", () => {
    const [{ data }, dataGetters, createCard] = makeNewDuelContext();

    createCard({ basePower: 5 }, "1-hnd");
    const locationId = "1-hnd";
    const cardInstanceIds = dataGetters.getCardsInstanceIdsAtLocation(locationId);
    expect(cardInstanceIds).toEqual(data.locations[locationId].cards);
  });

  it("getCardLocation() should return the correct location id", () => {
    const [{ data }, dataGetters, createCard] = makeNewDuelContext();

    const instanceId = createCard({ basePower: 5 }, "1-hnd");
    const locationId = dataGetters.getCardLocation(instanceId);
    expect(locationId).toEqual(data.cards[instanceId].locationId);
  });

  it("getCardPower() should return the correct power", () => {
    const [, dataGetters, createCard] = makeNewDuelContext();

    const instanceId = createCard({ basePower: 5 }, "1-hnd");
    const power = dataGetters.getCardPower(instanceId);
    expect(power).toEqual(5);
  });

  it("getCardStars() should return the correct stars", () => {
    const [, dataGetters, createCard] = makeNewDuelContext();

    const instanceId = createCard({ baseStars: 3 }, "1-hnd");
    const stars = dataGetters.getCardStars(instanceId);
    expect(stars).toEqual(3);
  });

  it("getCardKeywords() should return the correct keywords", () => {
    const [, dataGetters, createCard] = makeNewDuelContext();

    const instanceId = createCard({ keywords: [Keyword.BERSERK, Keyword.RUSH] }, "1-hnd");
    const keywords = dataGetters.getCardKeywords(instanceId);
    expect(keywords).toEqual([Keyword.BERSERK, Keyword.RUSH]);
  });
});
