import { LocationId } from "../data/LocationId";
import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";
import { MOCK_CARD_MODELS } from "./mock-cards";

import type { DuelStateData } from "../data/DuelStateData";
import type { DuelStateContext } from "../DuelStateContext";
import type { CardInstanceId } from "../data/primitives";

describe("Mock Cards", () => {
  it("test setup must run without errors", () => {
    const data = createInitialDuelStateData();
    const context = createDuelContext(data);
    const { facade, mutators } = context;

    {
      const CARDS_PER_DECK = 10;
      let i = CARDS_PER_DECK;
      while (i--) {
        const cmodel = MOCK_CARD_MODELS[i] as DuelStateData.CardModel;
        mutators.createCard(cmodel, "1-dck");
      }
      i = CARDS_PER_DECK;
      while (i--) {
        const cmodel = MOCK_CARD_MODELS[i] as DuelStateData.CardModel;
        mutators.createCard(cmodel, "2-dck");
      }
    }

    {
      const cleanup = [] as (() => void)[];
      const pass = {
        raisePower(card: DuelStateContext["facade"]["cards"][number], amount: number) {
          if (!card) throw new Error("Card not found");
          if (!card.instanceId) throw new Error("Card has no instanceId");
          const mod = mutators.addModifier({ cardInstanceId: card.instanceId, powerOffset: amount });
          cleanup.push(() => mutators.removeModifier(mod));
        },
      };

      function refresh() {
        cleanup.forEach(fn => fn());
        cleanup.length = 0;

        const card = facade.cards[10];
        const cardIsInPlay = LocationId.isField(card.location.id);

        function execute(functionBody: string) {
          const ctx = { card, pass };
          const fn = new Function(...Object.keys(ctx), `return eval(\`${functionBody}\`)`);
          return fn(...Object.values(ctx));
        }

        for (const fx of card.model.effects) {
          if (fx.type !== "passive") continue;
          if (!cardIsInPlay && !fx.worksOutOfPlay) continue;

          const conditionMet = execute(fx.condition);
          if (!conditionMet) continue;

          execute(fx.implementation);
        }

        // console.log(`After: ${card.power}`);
      }

      {
        function foo(ciid: number, to: any) {
          mutators.moveCardToLocation(ciid as CardInstanceId, to);
          refresh();
          // console.log(`Moved card ${ciid} to ${to}, 1's power is now ${facade.cards[1].power}`);
        }

        // console.log("Card 1 power is", facade.cards[1].power);
        foo(1, "1-fu1");
        foo(2, "1-hnd");
        foo(3, "1-hnd");
        foo(10, "1-fu1");
        foo(4, "1-hnd");
        foo(5, "1-hnd");
        foo(10, "1-grv");
      }
    }
  });
});
