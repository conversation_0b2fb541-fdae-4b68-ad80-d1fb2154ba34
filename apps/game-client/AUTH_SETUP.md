# 🔐 Clerk Authentication Setup

This guide will help you set up Clerk authentication with Google OAuth for your Drimgar Game client.

## Prerequisites

1. A Clerk account (sign up at [clerk.com](https://clerk.com))
2. A Convex project (run `npx convex dev` to create one)

## Step 1: Set up Clerk Application

1. Go to the [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new application or select an existing one
3. In the application settings:
   - Go to "User & Authentication" > "Social Connections"
   - Enable Google OAuth
   - Configure your Google OAuth credentials (from Google Cloud Console)
4. Note your publishable key from the "API Keys" section

## Step 2: Configure Environment Variables

Create a `.env.local` file in the `apps/game-client` directory:

```bash
# Convex URL (get this from running npx convex dev)
VITE_CONVEX_URL=https://your-project.convex.cloud

# Clerk configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

## Step 3: Configure Convex for Clerk

1. Set the Clerk JWT template in your Convex deployment:
   ```bash
   npx convex env set CLERK_JWT_ISSUER_DOMAIN https://your-clerk-domain.clerk.accounts.dev
   ```

2. In your Clerk dashboard, go to "JWT Templates" and create a new template for Convex:
   - Template name: `convex`
   - Include in token: Standard claims and any custom claims you need

## Step 4: Deploy and Test

1. Start the development server:
   ```bash
   pnpm dev
   ```

2. Deploy your Convex functions:
   ```bash
   npx convex deploy
   ```

3. Visit `http://localhost:5173` and test the "Continue with Google" button

## User Data Structure

When a user signs in for the first time, the system automatically creates a user record with:
- **name**: User's display name from Google
- **email**: User's email address
- **image**: Profile picture URL
- **emailVerified**: Email verification status
- **bio**: Empty string (can be updated by user)
- **decks**: Empty array as JSON string (for game data)

## Troubleshooting

- **"Clerk publishable key not found"**: Ensure `VITE_CLERK_PUBLISHABLE_KEY` is set in your `.env.local`
- **"Invalid JWT issuer"**: Make sure the Clerk JWT issuer domain is set correctly in Convex
- **"Convex URL not found"**: Run `npx convex dev` to get your deployment URL
- **"User creation failed"**: Check the Convex logs for detailed error messages

## Security Notes

- Never commit your `.env.local` file to version control
- The `.gitignore` file is already configured to exclude environment files
- Use different Clerk applications for development and production
- Regularly review your Clerk dashboard for security settings 