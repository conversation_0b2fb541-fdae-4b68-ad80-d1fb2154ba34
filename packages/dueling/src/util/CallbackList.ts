export class CallbackList<T extends (...args: any[]) => any> {
  private readonly callbacks = [] as T[];

  add(callback: T) {
    this.callbacks.push(callback);

    return () => {
      this.remove(callback);
    };
  }

  remove(callback: T) {
    const index = this.callbacks.indexOf(callback);
    if (index === -1) return;
    this.callbacks.splice(index, 1);
  }

  call(...args: Parameters<T>) {
    for (const callback of this.callbacks) {
      callback(...args);
    }
  }

  callAndPurge(...args: Parameters<T>) {
    const callbacks = this.callbacks.slice();
    this.callbacks.length = 0;
    for (const callback of callbacks) {
      callback(...args);
    }
  }
}
