export type PlayerNumber = 1 | 2;

export type CardInstanceId = number & { __cardInstanceId: never };

export type BoardLaneNumber = 1 | 2 | 3 | 4 | 5;
export type LocationIdSuffix = "hnd" | "dck" | "grv" | `fu${BoardLaneNumber}` | `ft${BoardLaneNumber}` | "bys";
export type LocationId = `${PlayerNumber}-${LocationIdSuffix}`;

export enum Keyword {
  // GRAND = "grand",
  SNEAK = "sneak",

  NOMOVE = "nomove",
  NOATTACK = "noattack",
  NOTRIBUTE = "notribute",

  RUSH = "rush",
  SWAPPER = "swapper",
  SWIFT = "swift",
  PIERCING = "piercing",
  OVERKILL = "overkill",
  INCORPOREAL = "incorporeal",
  INVULNERABLE = "invulnerable",
  BERSERK = "berserk",

  //// Traps only ////

  GLOBAL = "global",
  PERSISTENT = "persistent",

  //// Other (decorative) ////

  TOKEN = "token",

  DEV = "dev",
}

export enum FlipManner {
  Combat = "combat",
  Manual = "manual",
  Silent = "silent",
}
