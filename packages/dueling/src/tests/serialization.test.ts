import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";
import { CardInstanceId, Keyword } from "../data/primitives";

import { stringify, parse } from "telejson";

describe("Serialization", () => {
  const data = createInitialDuelStateData();
  const context = createDuelContext(data);
  const { facade, mutators } = context;

  mutators.createCard({ basePower: 4 }, "1-hnd");
  mutators.createCard({ basePower: 8 }, "1-hnd");
  mutators.createCard({ keywords: [Keyword.RUSH] }, "2-hnd");

  mutators.addModifier({ cardInstanceId: 1 as CardInstanceId, powerOffset: 2 });
  mutators.addModifier({ cardInstanceId: 2 as CardInstanceId, keywordsAdded: [Keyword.SNEAK] });
  mutators.addModifier({ cardInstanceId: 3 as CardInstanceId, powerOffset: ctx => facade.cards[1].location.cardsCount });

  it("Serialization of state data should work without errors and produce a string", () => {
    const serialized = stringify(data, { space: 2 });
    expect(typeof serialized).toBe("string");

    const deserialized = parse(serialized);
    expect(typeof deserialized).toBe("object");
  });
});
