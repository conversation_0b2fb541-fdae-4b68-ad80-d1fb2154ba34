import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";
import { CardInstanceId, Keyword } from "../data/primitives";

describe("Dueling", () => {
  const data = createInitialDuelStateData();
  const context = createDuelContext(data);
  const { facade, mutators } = context;
  
  mutators.createCard({ basePower: 4 }, "1-hnd");
  mutators.createCard({ basePower: 8 }, "1-hnd");
  mutators.createCard({ keywords: [Keyword.RUSH] }, "2-hnd");

  it("card.power should return correct base power", () => {
    expect(facade.cards[1].power || 0).toBe(4);
    expect(facade.cards[2].power || 0).toBe(8);
  });

  it("card.power should return correct modified power", () => {
    const cardInstanceId = 1 as CardInstanceId;
    const card = facade.cards[cardInstanceId];
    expect(card.power || 0).toBe(4);
    const mod = mutators.addModifier({ cardInstanceId, powerOffset: 2 });
    expect(card.power || 0).toBe(6);
    mutators.removeModifier(mod);
    expect(card.power || 0).toBe(4);
  });

  it("card.power should return correct modified power", () => {
    const card = facade.cards[1];
    expect(card.power || 0).toBe(4);
    const mod = mutators.addModifier({ cardInstanceId: card.instanceId, powerOffset: () => card.location.cardsCount });
    expect(card.power || 0).toBe(6);
    mutators.removeModifier(mod);
    expect(card.power || 0).toBe(4);
  });

  it("card.keywords should return correct base keywords", () => {
    expect(facade.cards[1].keywords).toEqual([]);
    expect(facade.cards[2].keywords).toEqual([]);
  });

  it("card.keywords should return correct modified keywords", () => {
    const card = facade.cards[1];
    expect(card.keywords).toEqual([]);
    const mod = mutators.addModifier({ cardInstanceId: card.instanceId, keywordsAdded: [Keyword.SNEAK] });
    expect(card.keywords).toEqual([Keyword.SNEAK]);
    mutators.removeModifier(mod);
    expect(card.keywords).toEqual([]);
  });

  it("card.keywords should return correct modified keywords", () => {
    const card = facade.cards[3];
    expect(card.keywords).toEqual([Keyword.RUSH]);
    const mod = mutators.addModifier({ cardInstanceId: card.instanceId, keywordsRemoved: [Keyword.RUSH] });
    expect(card.keywords).toEqual([]);
    mutators.removeModifier(mod);
    expect(card.keywords).toEqual([Keyword.RUSH]);
  });
});
