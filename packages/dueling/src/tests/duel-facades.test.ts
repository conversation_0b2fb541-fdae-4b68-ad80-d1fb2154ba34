import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";
import { LocationId } from "../data/primitives";
import { CardModelBlueprint } from "../serialization/CardModelBlueprint";

const TEST_LANES_COUNT = 5;
function makeNewDuelContext() {
  const data = createInitialDuelStateData(TEST_LANES_COUNT);
  const context = createDuelContext(data);

  function createCard(modelBlueprint: CardModelBlueprint, lotId: LocationId) {
    const modelData = CardModelBlueprint.toData(modelBlueprint);
    const ciid = context.mutators.createCard(modelData, lotId);
    const card = context.facade.cards[ciid];
    if (!card) throw new Error("Card not found");
    return card;
  }

  return [context, createCard] as const;
}

describe("Duel state facades", () => {
  {
    const [{ facade, getters }] = makeNewDuelContext();

    it("", () => {
      const p1 = facade.players[1];
      const p2 = facade.players[2];
      expect(p1.playerNumber).toBe(1);
      expect(p2.playerNumber).toBe(2);
      expect(p1.opponent).toBe(p2);
      expect(p2.opponent).toBe(p1);

      expect(p1.locations.length).toBe(TEST_LANES_COUNT * 2 + 4);
      expect(p1.fields.length).toBe(TEST_LANES_COUNT * 2);
      expect(p1.fieldsUnit.length).toBe(TEST_LANES_COUNT);
      expect(p1.fieldsTrap.length).toBe(TEST_LANES_COUNT);

      expect(p1.hand).toBe(facade.locations["1-hnd"]);
      expect(p1.deck).toBe(facade.locations["1-dck"]);
      expect(p1.grave).toBe(facade.locations["1-grv"]);
      expect(p1.abyss).toBe(facade.locations["1-bys"]);
    });

    it("Duel state data getters should return the correct values", () => {
      const p1fields = ["1-fu1", "1-fu2", "1-fu3", "1-fu4", "1-fu5"];
      const p2fields = ["2-fu1", "2-fu2", "2-fu3", "2-fu4", "2-fu5"];

      for (let i = 0; i < 5; i++) {
        const p1field = facade.locations[p1fields[i]];
        const p2field = facade.locations[p2fields[i]];
        expect(p1field.id).toBe(p1fields[i]);
        expect(p2field.id).toBe(p2fields[i]);
        expect(getters.opposingUnitField(p1field).id).toBe(p2field.id);
        expect(getters.opposingUnitField(p2field).id).toBe(p1field.id);
      }
    });
  }

  // it("Location getters should return the correct references", () => {
  //   const { getters, facade } = makeNewDuelContext();

  //   expect(getters.opposingUnitField('1-fu1')).toBe(facade.locations['2-fu1']);
  //   facade.locations['1-fu1']
  // });

  // it("Location getters should return the correct references", () => {
  //   const { mutators, facade } = makeNewDuelContext();

  //   // mutators.createCard({ basePower: 4 }, "1-hnd");
  //   // mutators.createCard({ basePower: 8 }, "1-hnd");
  // });

  // mutators.createCard({ basePower: 4 }, "1-hnd");
  // mutators.createCard({ basePower: 8 }, "1-hnd");
  // mutators.createCard({ keywords: [Keyword.RUSH] }, "2-hnd");

  // it("card.power should return correct base power", () => {
  //   expect(facade.cards[1].power || 0).toBe(4);
  //   expect(facade.cards[2].power || 0).toBe(8);
  // });

  // it("card.power should return correct modified power", () => {
  //   const cardInstanceId = 1 as CardInstanceId;
  //   const card = facade.cards[cardInstanceId];
  //   expect(card.power || 0).toBe(4);
  //   const mod = mutators.addModifier({ cardInstanceId, powerOffset: 2 });
  //   expect(card.power || 0).toBe(6);
  //   mutators.removeModifier(mod);
  //   expect(card.power || 0).toBe(4);
  // });

  // it("card.power should return correct modified power", () => {
  //   const card = facade.cards[1];
  //   expect(card.power || 0).toBe(4);
  //   const mod = mutators.addModifier({ cardInstanceId: card.instanceId, powerOffset: () => card.location.cardsCount });
  //   expect(card.power || 0).toBe(6);
  //   mutators.removeModifier(mod);
  //   expect(card.power || 0).toBe(4);
  // });

  // it("card.keywords should return correct base keywords", () => {
  //   expect(facade.cards[1].keywords).toEqual([]);
  //   expect(facade.cards[2].keywords).toEqual([]);
  // });

  // it("card.keywords should return correct modified keywords", () => {
  //   const card = facade.cards[1];
  //   expect(card.keywords).toEqual([]);
  //   const mod = mutators.addModifier({ cardInstanceId: card.instanceId, keywordsAdded: [Keyword.SNEAK] });
  //   expect(card.keywords).toEqual([Keyword.SNEAK]);
  //   mutators.removeModifier(mod);
  //   expect(card.keywords).toEqual([]);
  // });

  // it("card.keywords should return correct modified keywords", () => {
  //   const card = facade.cards[3];
  //   expect(card.keywords).toEqual([Keyword.RUSH]);
  //   const mod = mutators.addModifier({ cardInstanceId: card.instanceId, keywordsRemoved: [Keyword.RUSH] });
  //   expect(card.keywords).toEqual([]);
  //   mutators.removeModifier(mod);
  //   expect(card.keywords).toEqual([Keyword.RUSH]);
  // });
});
