import { Operations } from "../gameplay/operations";
import { CardInstanceId, Keyword, LocationId, PlayerNumber } from "./primitives";

/**
 * Everything here must be serializable.
 */

type Code = string;

export module DuelStateData {
  export enum UnitActionType {
    ManualDeploy = "deploy",
    ManualAttack = "attack",
    ManualRelocate = "relocate",
  }

  export type TurnState = {
    playerNumber: PlayerNumber | null;
    turnNumber: number;

    readonly actionsMade: { [card: CardInstanceId]: UnitActionType[] };
  };

  export type Player = {
    //// Immutable

    readonly playerNumber: PlayerNumber;

    //// Mutable
    healthPoints: number;
    actionPoints: number;
    actionPointsCap: number;
  };

  export type Location = {
    readonly cards: CardInstanceId[];
  };

  export enum CardType {
    Unit = 0,
    Trap = 1,
  }

  export type CardEffect =
    | {
        readonly type: "passive";
        readonly locationTypes: ("field" | "hand" | "grave")[];
        readonly condition: Code;
        readonly implementation: Code;
      }
    | {
        readonly type: "active";
        readonly operationType: Operations.OperationType;
        readonly operationStatus: Operations.OperationStatus;
        readonly locationTypes: ("field" | "hand" | "grave")[];
        readonly condition: Code;
        readonly implementation: Code;
      }
    | {
        readonly type: "override";
        readonly blockDeploymentTo: Code;
      };

  export type CardModel = {
    readonly type: CardType;
    readonly basePower: number | null;
    readonly baseStars: number | null;
    readonly keywords: Keyword[];

    readonly effects: CardEffect[];

    //// Non-gameplay-related

    readonly guid: string;
    readonly slug: string;

    readonly name: string;
    readonly lore: string;
    readonly description: string;
    
    readonly art: string;
  };

  export type CardInstance = {
    //// Immutable

    readonly instanceId: CardInstanceId;

    readonly model: CardModel;
    readonly ownerPlayerNum: PlayerNumber;

    //// Mutable

    faceUp: boolean;

    locationId: LocationId;
    prevLocationId: LocationId;
  };

  export type ActiveModifier = {
    readonly cardInstanceId: CardInstanceId;
    readonly powerOffset?: number | ((ctx: any) => number);
    readonly starsOffset?: number | ((ctx: any) => number);
    readonly keywordsAdded?: Keyword[];
    readonly keywordsRemoved?: Keyword[];
    readonly faceUp?: boolean;

    // readonly expiry?: Code | ((operation: Operations.OperationInstance) => boolean);
    readonly expiry?: (operation: Operations.OperationInstance<any>) => boolean;
    readonly ephemeral?: boolean;
  };

  export type FullState = {
    phase: "pre" | "ongoing" | "over";
    readonly turn: TurnState;
    readonly players: { readonly 1: Player; readonly 2: Player };
    readonly cards: Record<CardInstanceId, CardInstance>;
    readonly locations: Record<LocationId, Location>;
    readonly activeModifiers: ActiveModifier[];
  };
}

export type DuelStateData = DuelStateData.FullState;
