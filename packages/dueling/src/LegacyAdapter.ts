import { Dueling } from "@drimgar/dueling";

import { Dueling as __LegacyDueling } from "@drimgar/dueling-legacy";
import { Operations } from "./exports";
import { CardModelBlueprint } from "./serialization/CardModelBlueprint";
import { serializeOperationEvent } from "./serialization/serializeOperationEvent";

type __LegacyDuelInstanceType = InstanceType<typeof __LegacyDueling.Duel>;
type __LegacyFuncIni = __LegacyDuelInstanceType["serializer"]["$$ini"];
type __LegacyFuncGo = __LegacyDuelInstanceType["serializer"]["$$go"];
type __LegacyDuelSomethingResults = ReturnType<__LegacyFuncIni> | ReturnType<__LegacyFuncGo>;
type __LegacyDuelSomethingResult = __LegacyDuelSomethingResults[1];

type AdaptedResult = Omit<__LegacyDuelSomethingResult, "static" | "events"> & {
  events: Dueling.OperationEvent[];
  static: {
    cardModelsBySlug: Record<string, Dueling.DuelStateData.CardModel>;
    cardModelSlugsByInstanceId: Record<number, string>;
  };
};
type AdaptedResults = [null, AdaptedResult, AdaptedResult];
type AdaptedFuncIni = (...params: Parameters<__LegacyFuncIni>) => AdaptedResults;
type AdaptedFuncGo = (...params: Parameters<__LegacyFuncGo>) => AdaptedResults;

type __LegacyFuncUpdateLibrary = __LegacyDuelInstanceType["updateLibrary"];
type AdaptedFuncUpdateLibrary = (...params: Parameters<__LegacyFuncUpdateLibrary>) => void;

export function createLegacyAdapterInstance(
  data: Dueling.DuelStateData.FullState,
  duelConfiguration: Dueling.DuelConfiguration
) {
  const context = Dueling.createDuelContext(data, duelConfiguration);
  const gameplay = Dueling.createDuelGameplayController(data, duelConfiguration);

  const staticDataStore: AdaptedResult["static"] = {
    cardModelsBySlug: {},
    cardModelSlugsByInstanceId: [],
  };

  gameplay.onOperationStatusChangeHandlers.add(operation => {
    if (Operations.isType(operation, "SpawnCard") && operation.state.status === "ended") {
      const { card, cardModel } = operation.props;
      staticDataStore.cardModelsBySlug[cardModel.slug] = cardModel;
      staticDataStore.cardModelSlugsByInstanceId[card!.instanceId] = cardModel.slug;
    }
  });

  type ProperResult =
    | ReturnType<typeof gameplay.prepareBoard>
    | ReturnType<typeof gameplay.startMatch>
    | ReturnType<typeof gameplay.submitCommand>;

  function adaptResults(result: ProperResult): AdaptedResults {
    function adaptSubjectiveResult(result: ProperResult, playerNum: Dueling.PlayerNumber): AdaptedResult {
      const events = result.events.getEventHistory();
      const serializedEvents = events.map(event => event && serializeOperationEvent(event));

      return {
        playerNum,
        errors: [],
        events: serializedEvents,
        state: result.subjectiveStates[playerNum],
        static: staticDataStore,
      };
    }

    console.log(`🐬`, [null, adaptSubjectiveResult(result, 1), adaptSubjectiveResult(result, 2)]);

    return [null, adaptSubjectiveResult(result, 1), adaptSubjectiveResult(result, 2)] as const;
  }

  const updateLibrary: AdaptedFuncUpdateLibrary = legacyCardModels => {
    const effects = [] as NonNullable<CardModelBlueprint["effects"]>;
    for (const legacyModel of legacyCardModels) {
      if (legacyModel.effects) {
        if (legacyModel.effects.active) {
          const fx = legacyModel.effects.active;
          effects.push({
            type: "active",
            condition: fx.condition,
            implementation: fx.perform,
            locationTypes: fx.allowedLots as ("field" | "hand" | "grave")[],
            operationStatus: fx.eventStatus as "began" | "ended" | "failed",
            operationType: fx.eventName as any,
          });
        }

        if (legacyModel.effects.passive) {
          const fx = legacyModel.effects.passive;
          effects.push({
            type: "passive",
            condition: fx.condition,
            implementation: fx.perform,
            locationTypes: fx.allowedLots as ("field" | "hand" | "grave")[],
          });
        }

        if (legacyModel.effects.flip) {
          const fx = legacyModel.effects.flip;
          effects.push({
            type: "flip",
            implementation: fx.perform,
            manner:
              fx.manner === __LegacyDueling.FlipManner.Combat
                ? "combat"
                : fx.manner === __LegacyDueling.FlipManner.Manual
                ? "safe"
                : "safe",
          });
        }

        if (legacyModel.effects.overrides) {
          const fx = legacyModel.effects.overrides;
          effects.push({
            type: "override",
            blockDeploymentTo: fx.blockDeploymentTo,
          });
        }
      }

      staticDataStore.cardModelsBySlug[legacyModel.slug] = CardModelBlueprint.toData({
        guid: legacyModel.guid,
        slug: legacyModel.slug,
        type: legacyModel.type,
        description: legacyModel.description,
        basePower: legacyModel.power,
        baseStars: legacyModel.tags?.includes(__LegacyDueling.Tag.GRAND) ? 1 : 0,
        keywords: legacyModel.tags as string[] as Dueling.Keyword[],
        effects: effects,
      });
    }
  };

  const initialize: AdaptedFuncIni = (deckOneStrings, deckTwoStrings) => {
    const deckOne = deckOneStrings.map(slug => context.library.getBySlug(slug));
    const deckTwo = deckTwoStrings.map(slug => context.library.getBySlug(slug));

    const results = gameplay.prepareBoard(deckOne, deckTwo);

    return adaptResults(results);
  };

  const handleCommandOrReady: AdaptedFuncGo = cmd => {
    if (!cmd) throw new Error("cmd is required");

    const runAppropriateOperation = () => {
      switch (cmd.type) {
        case "ready":
          if (data.phase !== "pre") throw new Error(`Cannot ready when phase is ${data.phase}`);
          return gameplay.startMatch(1);
        case "draw":
          return gameplay.submitCommand("draw", cmd.player as Dueling.PlayerNumber);
        case "deploy":
          return gameplay.submitCommand(
            "deploy",
            cmd.player as Dueling.PlayerNumber,
            cmd.card as Dueling.CardInstanceId,
            cmd.to as Dueling.LocationId
          );
        case "reveal":
          return gameplay.submitCommand(
            "reveal",
            cmd.player as Dueling.PlayerNumber,
            cmd.card as Dueling.CardInstanceId
          );
        case "attack":
          return gameplay.submitCommand(
            "attack",
            cmd.player as Dueling.PlayerNumber,
            cmd.card as Dueling.CardInstanceId
          );
        case "relocate":
          return gameplay.submitCommand(
            "relocate",
            cmd.player as Dueling.PlayerNumber,
            cmd.card as Dueling.CardInstanceId,
            cmd.to as Dueling.LocationId
          );
        case "endturn":
          return gameplay.submitCommand("endturn", cmd.player as Dueling.PlayerNumber);
        case "surrender":
          return gameplay.submitCommand("surrender", cmd.player as Dueling.PlayerNumber);
        default:
          throw new Error(`Unknown command type: ${cmd.type}`);
      }
    };

    const results = runAppropriateOperation();
    return adaptResults(results);
  };

  return {
    updateLibrary,
    ini: initialize,
    go: handleCommandOrReady,
  };
}

export type LegacyAdapter = ReturnType<typeof createLegacyAdapterInstance>;
export type LegacyAdapterResults = ReturnType<LegacyAdapter["ini"]> | ReturnType<LegacyAdapter["go"]>;
