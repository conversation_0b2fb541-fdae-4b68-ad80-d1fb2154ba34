import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    email: v.string(),
    image: v.optional(v.string()),
    emailVerified: v.optional(v.string()),
    authId: v.string(), // This will be the Clerk user ID
    bio: v.string(),
    decks: v.string(), // JSON string of decks array
  })
    .index("by_auth_id", ["authId"])
    .index("by_email", ["email"]),
}); 