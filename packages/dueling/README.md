# @drimgar/dueling

A TypeScript library for managing card game duels with a complete state management system, operations framework, and event handling.

## Overview

This package provides a comprehensive dueling system that handles:
- Game state management and mutations
- Player commands and operations
- Card effects and interactions
- Event history and subjective state views
- Serialization for network play

## Installation

```bash
npm install @drimgar/dueling
```

## Quick Start

### 1. Initialize a New Duel

```typescript
import { 
  createInitialDuelStateData, 
  createDuelGameplayController,
  CardModelBlueprint 
} from '@drimgar/dueling';

// Create initial game state
const gameState = createInitialDuelStateData();

// Create gameplay controller
const gameplay = createDuelGameplayController(gameState, {
  settings: {
    lanesCount: 9,
    initialHandSizeFirst: 2,
    initialHandSizeSecond: 4,
    initialHealthPoints: 11,
    initialActionPointsFirst: 1,
    initialActionPointsSecond: 2,
    maxActionPoints: 3,
  },
  features: {
    healPlayerOnTurnEnd: true,
    cardsToDrawOnTurnStart: 1,
    unitCost: 1,
  }
});
```

### 2. Prepare Decks and Start Match

```typescript
// Define card blueprints for each deck
const deckOneBlueprints: CardModelBlueprint[] = [
  {
    guid: "card-1",
    slug: "fire-warrior",
    type: "unit",
    description: "A fierce warrior",
    basePower: 3,
    baseStars: 0,
    keywords: ["RUSH"],
    effects: []
  },
  // ... more cards
];

const deckTwoBlueprints: CardModelBlueprint[] = [
  // ... player 2's deck
];

// Prepare the board with decks
const prepareResult = gameplay.prepareBoard(deckOneBlueprints, deckTwoBlueprints);

// Start the match (player 1 goes first)
const startResult = gameplay.startMatch(1);
```

### 3. Handle Player Commands

```typescript
import { SerializedCommand } from '@drimgar/dueling';

// Example commands
const drawCommand: SerializedCommand = {
  type: "draw",
  player: 1
};

const deployCommand: SerializedCommand = {
  type: "deploy",
  player: 1,
  card: 1, // CardInstanceId
  to: "1-fld-1" // LocationId
};

const attackCommand: SerializedCommand = {
  type: "attack",
  player: 1,
  card: 1
};

// Submit commands
const result = gameplay.submitCommand(drawCommand);

// Access operation results
console.log(result.operation.state.status); // "ended" | "failed" | "began"
console.log(result.events.getHistory()); // Event history
console.log(result.subjectiveStates[1]); // Player 1's view of game state
```

## Core Concepts

### Game State Structure

The game state contains:
- **Players**: Health, action points, player-specific data
- **Cards**: All card instances with their current state
- **Locations**: Deck, hand, field positions, graveyard
- **Turn**: Current player, turn number, actions made
- **Phase**: "pre" | "ongoing" | "over"

### Operations System

All game actions are handled through operations:

```typescript
// Operations are automatically triggered by commands
// But you can also run them directly for testing:

const { context } = gameplay;
const { facade, mutators } = context;

// Direct operation example (for testing/debugging)
import { createOperationRunner } from '@drimgar/dueling';
const runner = createOperationRunner(context, onStatusChange);

const operation = runner.runOperation("DrawCard", {
  player: facade.players[1],
  count: 2
}, null);
```

### Event Handling

Monitor game events in real-time:

```typescript
gameplay.onOperationStatusChangeHandlers.add((operation) => {
  console.log(`Operation ${operation.type} ${operation.state.status}`);
  
  if (operation.state.status === "ended") {
    // Operation completed successfully
    console.log("Operation props:", operation.props);
  }
});
```

### Subjective State Views

Each player sees only what they should see:

```typescript
const result = gameplay.submitCommand(command);

// Player 1's view (hidden opponent cards are obscured)
const player1View = result.subjectiveStates[1];

// Player 2's view
const player2View = result.subjectiveStates[2];

// Use these for sending state updates to clients
sendToPlayer1(player1View);
sendToPlayer2(player2View);
```

## Available Commands

| Command | Description | Parameters |
|---------|-------------|------------|
| `draw` | Draw cards | `player` |
| `deploy` | Deploy unit/trap to field | `player`, `card`, `to` |
| `relocate` | Move unit to different position | `player`, `card`, `to` |
| `attack` | Attack with unit | `player`, `card` |
| `reveal` | Reveal face-down unit | `player`, `card` |
| `endturn` | End current turn | `player` |
| `surrender` | Surrender the match | `player` |

## Server Integration Example

```typescript
// Server-side game session
class DuelSession {
  private gameplay: DuelGameplayController;
  private players: { [playerId: string]: PlayerNumber } = {};

  constructor(player1Id: string, player2Id: string, decks: [CardModelBlueprint[], CardModelBlueprint[]]) {
    const gameState = createInitialDuelStateData();
    this.gameplay = createDuelGameplayController(gameState, defaultConfig);
    
    this.players[player1Id] = 1;
    this.players[player2Id] = 2;
    
    // Setup game
    this.gameplay.prepareBoard(decks[0], decks[1]);
    this.gameplay.startMatch(1);
    
    // Listen for events
    this.gameplay.onOperationStatusChangeHandlers.add(this.handleOperation.bind(this));
  }

  handlePlayerCommand(playerId: string, command: Omit<SerializedCommand, 'player'>) {
    const playerNumber = this.players[playerId];
    if (!playerNumber) throw new Error("Invalid player");

    const fullCommand: SerializedCommand = { ...command, player: playerNumber };
    const result = this.gameplay.submitCommand(fullCommand);
    
    // Send updates to both players
    this.sendStateUpdate(1, result.subjectiveStates[1]);
    this.sendStateUpdate(2, result.subjectiveStates[2]);
    
    return result;
  }

  private handleOperation(operation: Operations.OperationInstance) {
    // Log game events, check win conditions, etc.
  }

  private sendStateUpdate(playerNumber: PlayerNumber, state: any) {
    // Send to appropriate client
  }
}
```

## Testing

```typescript
import { createDuelContext, createInitialDuelStateData } from '@drimgar/dueling';

describe("Duel Tests", () => {
  it("should handle basic gameplay", () => {
    const data = createInitialDuelStateData();
    const context = createDuelContext(data);
    const { facade, mutators } = context;
    
    // Create test cards
    mutators.createCard({ basePower: 4 }, "1-hnd");
    
    expect(facade.cards[1].power).toBe(4);
  });
});
```

## Configuration Options

### Settings
- `lanesCount`: Number of field positions (default: 9)
- `initialHandSizeFirst/Second`: Starting hand sizes
- `initialHealthPoints`: Starting health
- `initialActionPointsFirst/Second`: Starting action points
- `maxActionPoints`: Maximum action points per turn

### Features
- `healPlayerOnTurnEnd`: Heal for unspent action points
- `cardsToDrawOnTurnStart`: Cards drawn each turn
- `unitCost`: Action point cost for deploying units
- `skipDeckShuffling`: Disable shuffling for testing

## API Reference

### Main Exports
- `createDuelGameplayController()`: Main controller factory
- `createInitialDuelStateData()`: Initial state factory
- `createDuelContext()`: Context factory for direct state access
- `Operations`: All available game operations
- `SerializedCommand`: Command type definitions
- `CardModelBlueprint`: Card definition interface

### Types
- `DuelGameplayController`: Main controller interface
- `PlayerNumber`: 1 | 2
- `CardInstanceId`: Unique card instance identifier
- `LocationId`: Game location identifier
- `Keyword`: Card keywords (RUSH, SNEAK, BERSERK, etc.)