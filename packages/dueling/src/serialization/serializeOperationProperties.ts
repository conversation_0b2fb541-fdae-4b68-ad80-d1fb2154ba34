import { Operations } from "../gameplay/operations";

export function serializeOperationProperties<TProps extends Operations.OperationProps>(props: TProps) {
  type FlattenIfArray<T> = T extends (infer R)[] ? R : T;
  type ValueTypes<T> = T extends { [k: string]: infer U } ? FlattenIfArray<U> : never;
  type UnionValues<T> = ValueTypes<T[keyof T]>;
  type PropValue = UnionValues<Operations.OperationPropsPerType>;

  const result = {} as any;
  for (const [key, value] of Object.entries(props)) {
    const $value = value as PropValue;

    if (typeof $value === "string") {
      result[key] = $value.toUpperCase();
    } else if (typeof $value === "number" || typeof $value === "boolean" || $value == null) {
      result[key] = $value;
    } else if ("id" in $value) {
      result[key] = $value.id;
    } else if ("instanceId" in $value) {
      result[key] = $value.instanceId;
    } else if ("playerNumber" in $value) {
      result[key] = $value.playerNumber;
    } else if (key === "command") {
      Object.assign(result, serializeOperationProperties($value));
    }
  }
  return result;
}
