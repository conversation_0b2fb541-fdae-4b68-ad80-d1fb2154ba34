import { CardInstanceId, LocationId, PlayerNumber } from "../exports";

export type SerializedCommand = { player: PlayerNumber } & (
  | { type: "draw" }
  | { type: "reveal"; card: CardInstanceId }
  | { type: "deploy"; card: CardInstanceId; to: LocationId }
  | { type: "attack"; card: CardInstanceId }
  | { type: "relocate"; card: CardInstanceId; to: LocationId }
  | { type: "endturn" }
  | { type: "surrender" }
);

export module SerializedCommand {
  export function fromParameters(type: "draw", player: PlayerNumber): SerializedCommand;

  export function fromParameters(type: "reveal", player: PlayerNumber, card: CardInstanceId): SerializedCommand;

  export function fromParameters(
    type: "deploy",
    player: PlayerNumber,
    card: CardInstanceId,
    to: LocationId
  ): SerializedCommand;

  export function fromParameters(type: "attack", player: PlayerNumber, card: CardInstanceId): SerializedCommand;

  export function fromParameters(
    type: "relocate",
    player: PlayerNumber,
    card: CardInstanceId,
    to: LocationId
  ): SerializedCommand;

  export function fromParameters(type: "endturn", player: PlayerNumber): SerializedCommand;

  export function fromParameters(type: "surrender", player: PlayerNumber): SerializedCommand;

  export function fromParameters<T extends SerializedCommand["type"]>(
    type: T,
    ...parameters: any[]
  ): SerializedCommand {
    switch (type) {
      case "draw":
        return { type, player: parameters[0] };
      case "reveal":
        return { type, player: parameters[0], card: parameters[1] };
      case "deploy":
        return { type, player: parameters[0], card: parameters[1], to: parameters[2] };
      case "attack":
        return { type, player: parameters[0], card: parameters[1] };
      case "relocate":
        return { type, player: parameters[0], card: parameters[1], to: parameters[2] };
      case "endturn":
        return { type, player: parameters[0] };
      case "surrender":
        return { type, player: parameters[0] };
      default:
        throw new Error(`Invalid command type: ${type}!`);
    }
  }
}
