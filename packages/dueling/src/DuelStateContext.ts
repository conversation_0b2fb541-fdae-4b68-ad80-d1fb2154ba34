import { DuelStateData } from "./data/DuelStateData";
import { createDataMutators } from "./data/createDataMutators";
import { DuelStateFacade } from "./facade/DuelStateFacade";
import { createFacadeAsserters } from "./facade/createFacadeAsserters";
import { createFacadeGetters } from "./facade/createFacadeGetters";

const defaultSettings = {
  lanesCount: 9,
  initialHandSizeFirst: 2,
  initialHandSizeSecond: 4,
  initialHealthPoints: 11,
  initialActionPointsFirst: 1,
  initialActionPointsSecond: 2,
  maxActionPoints: 3,
};

const defaultFeatures = {
  /**
   * Smart shuffle, which ensures (if possible) the first three cards are:
   * - a unit card that is not a grand unit
   * - a unit card
   * - a trap card
   */
  rigDeckShuffling: true,
  /**
   * Skip deck shuffling entirely.
   */
  skipDeckShuffling: false,
  /**
   * At the end of each player's turn, heal them for every unspent action point.
   */
  healPlayerOnTurnEnd: true,
  /**
   * At the start of each player's turn, draw this many cards.
   */
  cardsToDrawOnTurnStart: 1,
  /**
   * On empty deck, deal this much damage to the player for every card they would have drawn at the start of their turn.
   */
  damageOnTurnStartPerMissingDeckCard: 1,
  /**
   * Playing a unit card costs this much action points.
   */
  unitCost: 1,
  /**
   * Playing a trap card costs this much action points.
   */
  trapCost: 0,
};

const defaultCheats = {
  noActionPointChecks: false,
};

export type DuelConfiguration = {
  settings?: Partial<typeof defaultSettings>;
  features?: Partial<typeof defaultFeatures>;
  cheats?: Partial<typeof defaultCheats>;
};

export function createDuelContext(data: DuelStateData.FullState, customConfiguration: DuelConfiguration) {
  const configuration = {
    settings: { ...defaultSettings, ...customConfiguration?.settings },
    features: { ...defaultFeatures, ...customConfiguration?.features },
    cheats: { ...defaultCheats, ...customConfiguration?.cheats },
  };

  const facade = DuelStateFacade.createFromData(data);
  const mutators = createDataMutators(data);

  const facadeGetters = createFacadeGetters(facade);
  const facadeAsserters = createFacadeAsserters(facade, facadeGetters, configuration.cheats);

  const cardModelLibrary = {
    getBySlug(slug: string): Readonly<DuelStateData.CardModel> {
      //// Temporarily just create a new card model
      return {
        guid: "placeholder-guid",
        description: "placeholder-description",
        slug: slug,
        type: DuelStateData.CardType.Unit,
        basePower: 5,
        baseStars: 0,
        keywords: [],
        effects: [],
      };
    },
  };

  const context = {
    library: cardModelLibrary,
    data,
    facade,
    getters: facadeGetters,
    assert: facadeAsserters,
    mutators,
    configuration,
  };

  return context;
}
export type DuelStateContext = ReturnType<typeof createDuelContext>;
