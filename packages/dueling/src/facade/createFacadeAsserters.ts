import { DuelConfiguration, DuelStateContext } from "../DuelStateContext";
import { DuelStateData } from "../data/DuelStateData";
import { DuelStateFacade } from "./DuelStateFacade";
import { PlayerCommand } from "../gameplay/commands";
import { DuelStateFacadeGetters } from "./createFacadeGetters";
import { Keyword } from "../data/primitives";

export function createFacadeAsserters(
  facade: DuelStateFacade,
  facadeGetters: DuelStateFacadeGetters,
  cheats: DuelConfiguration["cheats"]
) {
  const Util = {
    toLocation(from: DuelStateFacade.Card | DuelStateFacade.Location) {
      if ("location" in from) return from.location;
      if ("id" in from) return facade.locations[from.id];
      throw new Error("Invalid argument");
    },
    toPlayer(from: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player) {
      if ("controller" in from) return from.owner;
      if ("owner" in from) return from.owner;
      if ("playerNumber" in from) return facade.players[from.playerNumber];
      throw new Error("Invalid argument");
    },
  };

  function assert(value: any, message: string): asserts value {
    if (!value) throw new Error(message);
  }

  const assertFunctions = {
    custom: assert as (value: any, message: string) => void, //// asserts value,
    equals: <T extends DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player>(
      actual: T,
      expected: T,
      message: string = `Expected ${actual} to equal ${expected}`
    ) => {
      if ("instanceId" in actual && "instanceId" in expected) {
        assert(actual.instanceId === expected.instanceId, message);
      } else if ("id" in actual && "id" in expected) {
        assert(actual.id === expected.id, message);
      } else if ("playerNumber" in actual && "playerNumber" in expected) {
        assert(actual.playerNumber === expected.playerNumber, message);
      } else throw new Error("Invalid arguments");
    },

    isCurrentPlayer: (player: DuelStateFacade.Player) =>
      assert(facadeGetters.currentPlayer === player, "It's not that player's turn"),
    isNotCurrentPlayer: (player: DuelStateFacade.Player) =>
      assert(facadeGetters.currentPlayer !== player, "It's that player's turn"),
    hasTurn: (entity: DuelStateFacade.Player | DuelStateFacade.Location | DuelStateFacade.Card) => {
      const player = "playerNumber" in entity ? entity : entity.owner;
      assert(player, "Failed to retrieve player from entity");
      assert(facadeGetters.currentPlayer === player, "It's not this entity's turn");
    },

    hasKeyword: (card: DuelStateFacade.Card, ...keywords: Keyword[]) => {
      keywords.forEach(keyword => assert(card.keywords.includes(keyword), `Card does not have #${keyword} keyword`));
    },
    hasNotKeyword: (card: DuelStateFacade.Card, ...keywords: Keyword[]) => {
      keywords.forEach(keyword => assert(!card.keywords.includes(keyword), `Card has #${keyword} keyword`));
    },
    isFaceUp: (card: DuelStateFacade.Card) => assert(card.isFaceUp, "Card is not face up"),
    isFaceDown: (card: DuelStateFacade.Card) => assert(card.isFaceDown, "Card is not face down"),
    isInPlay: (card: DuelStateFacade.Card) => assert(card.location.isField, "Card is not on any field"),

    notAnyActionsPerformedThisTurn: (card: DuelStateFacade.Card, ...actionTypes: DuelStateData.UnitActionType[]) =>
      assert(
        !facadeGetters.unitMadeSomeActionsThisTurn(card, ...actionTypes),
        `Unit already performed action(s) of type ${actionTypes
          .map(s => s.toUpperCase())
          .join(" or ")} this turn, and is exhausted`
      ),
    notAllActionsPerformedThisTurn: (card: DuelStateFacade.Card, ...actionTypes: DuelStateData.UnitActionType[]) =>
      assert(
        !facadeGetters.unitMadeAllActionsThisTurn(card, ...actionTypes),
        `Unit already performed action(s) of type ${actionTypes
          .map(s => s.toUpperCase())
          .join(" and ")} this turn, and is exhausted`
      ),

    isNotFreshlyDeployed: (card: DuelStateFacade.Card) =>
      assertFunctions.notAllActionsPerformedThisTurn(card, DuelStateData.UnitActionType.ManualDeploy),

    canManuallyReveal: (card: DuelStateFacade.Card) => {
      assert(card.isUnit, `Only unit-type cards can be revealed`);
      assertFunctions.hasTurn(card);
      assertFunctions.isInPlay(card);
      assertFunctions.isFaceDown(card);
      if (!card.hasKeyword(Keyword.RUSH)) assertFunctions.isNotFreshlyDeployed(card);
    },

    canManuallyAttack: (card: DuelStateFacade.Card) => {
      assertFunctions.hasTurn(card);
      if (!card.hasKeyword(Keyword.RUSH)) assertFunctions.isNotFreshlyDeployed(card);
      if (!card.hasKeyword(Keyword.SWIFT)) {
        assertFunctions.notAnyActionsPerformedThisTurn(
          card,
          DuelStateData.UnitActionType.ManualAttack,
          DuelStateData.UnitActionType.ManualRelocate
        );
      } else {
        assertFunctions.notAllActionsPerformedThisTurn(
          card,
          DuelStateData.UnitActionType.ManualAttack,
          DuelStateData.UnitActionType.ManualRelocate
        ); // Operationally, this is redundant, but provides clarity in terms of error text the user will see
        assertFunctions.notAllActionsPerformedThisTurn(card, DuelStateData.UnitActionType.ManualAttack);
      }
      assertFunctions.canAttack(card);
    },

    canManuallyRelocateTo: (card: DuelStateFacade.Card, to: DuelStateFacade.Location) => {
      assertFunctions.hasTurn(card);
      assertFunctions.isInPlay(card);
      if (!card.hasKeyword(Keyword.RUSH)) assertFunctions.isNotFreshlyDeployed(card);
      if (!card.hasKeyword(Keyword.SWIFT)) {
        assertFunctions.notAnyActionsPerformedThisTurn(
          card,
          DuelStateData.UnitActionType.ManualAttack,
          DuelStateData.UnitActionType.ManualRelocate
        );
      } else {
        assertFunctions.notAllActionsPerformedThisTurn(
          card,
          DuelStateData.UnitActionType.ManualAttack,
          DuelStateData.UnitActionType.ManualRelocate
        ); // Operationally, this is redundant, but provides clarity in terms of error text the user will see
        assertFunctions.notAllActionsPerformedThisTurn(card, DuelStateData.UnitActionType.ManualRelocate);
      }
      assertFunctions.canRelocateTo(card, to);
    },

    cardEffectBlocksDeploymentTo: (card: DuelStateFacade.Card, to: DuelStateFacade.Location) => {
      // assert(
      //   !card.effects.overrides ||
      //     !card.effects.overrides.blockDeploymentTo ||
      //     !card.effects.overrides.blockDeploymentTo(this.duel, card, to),
      //   "Unit's special effect does not allow for deployment here."
      // );
    },
    canManuallyDeployTo: (deployee: DuelStateFacade.Card, to: DuelStateFacade.Location) => {
      assertFunctions.isInHand(deployee);
      assertFunctions.isFriendly(deployee, to);

      if (deployee.isUnit) {
        assertFunctions.isUnitType(deployee);
        assertFunctions.isUnitField(to);
        assertFunctions.cardEffectBlocksDeploymentTo(deployee, to);
        if (facadeGetters.requiresTribute(deployee)) {
          assertFunctions.fieldHasRequiredDeploymentTribute(to, deployee);
        } else {
          assertFunctions.isUnoccupied(to);
        }
      } else if (deployee.isTrap) {
        assertFunctions.isTrapField(to);
      } else throw new Error(`What? (unrecognized card type: ${deployee.model.type})`);
    },

    fieldHasRequiredDeploymentTribute: (to: DuelStateFacade.Location, deployee: DuelStateFacade.Card) => {
      assertFunctions.isUnitField(to);

      const minimumRequiredStars = deployee.stars - 1;
      const tribute = to.topCard;
      assert(
        tribute,
        deployee.stars < 1
          ? `Field has no unit to tribute. This unit can only be deployed by replacing another unit.`
          : `Field has no unit to tribute. This unit can only be deployed by replacing another unit with ${minimumRequiredStars} or more stars.`
      );
      assertFunctions.isUnitType(tribute);
      assertFunctions.canBeTribute(tribute, deployee);
    },

    canBeTribute: (tribute: DuelStateFacade.Card, deployee: DuelStateFacade.Card) => {
      assertFunctions.hasNotKeyword(tribute, Keyword.NOTRIBUTE);

      if (!tribute.hasKeyword(Keyword.RUSH)) {
        assertFunctions.isNotFreshlyDeployed(tribute);
      }

      const minimumRequiredStars = deployee.stars - 1;
      assert(tribute.stars <= minimumRequiredStars, `Tribute unit must have ${minimumRequiredStars} or more stars.`);
    },

    // effectPossible<Tcontext extends ActiveEffectContext | PassiveEffectContext>(context: Tcontext) {
    //   assert(
    //     context.fx.isAllowedInCurrentLot,
    //     `Effect not possible in '${context.fx.card.lot!.type}' ` +
    //       //@ts-ignore
    //       `(must be ${context.fx.data.allowedLots})`
    //   );
    //   //@ts-ignore
    //   assert(context.fx.$condition(context), "Effect conditions no longer met");
    // }

    canDoFieldActions: (card: DuelStateFacade.Card) => {
      assertFunctions.isInPlay(card);
      assertFunctions.isFaceUp(card);
    },
    canRelocate: (card: DuelStateFacade.Card) => {
      assertFunctions.canDoFieldActions(card);
      assertFunctions.hasNotKeyword(card, Keyword.NOMOVE);
    },
    canRelocateTo: (card: DuelStateFacade.Card, to: DuelStateFacade.Location) => {
      assertFunctions.canRelocate(card);
      assert(to.isUnitField, "Destination must be a unit-field.");
      assert(card.location !== to, "Destination must not be the same as the unit's current Field.");
      assert(card.controller === to.owner, "Destination must be controlled by you.");
      if (!card.hasKeyword(Keyword.SWAPPER)) {
        assertFunctions.isUnoccupied(to);
      }
    },
    canAttack: (card: DuelStateFacade.Card) => {
      assertFunctions.canDoFieldActions(card);
      assertFunctions.hasNotKeyword(card, Keyword.NOATTACK);
    },
    canAttackDirectly: (card: DuelStateFacade.Card) => {
      assertFunctions.canAttack(card);
      if (card.hasKeyword(Keyword.PIERCING)) return;
      const defender = facadeGetters.opposingUnitField(card)?.topCard;
      const undefended = !defender || defender.hasKeyword(Keyword.INCORPOREAL);
      assert(undefended, `Line is defended by "${defender}"`);
    },
    canDie: (card: DuelStateFacade.Card) => {
      assert(card.isUnit, "Only units can die");
      assert(card.isInPlay, "Only units in play can die");
      assertFunctions.hasNotKeyword(card, Keyword.INVULNERABLE);
    },
    hasOpposingUnit: (from: DuelStateFacade.Card | DuelStateFacade.Location) =>
      assert(facadeGetters.opposingUnit(from) !== undefined, "There is no opposing unit"),
    isOpposingUnit: (
      target: DuelStateFacade.Card | DuelStateFacade.Location,
      from: DuelStateFacade.Card | DuelStateFacade.Location
    ) => assert(facadeGetters.opposingUnit(from) === target, "Card is not the opposing unit"),
    isOpposing: (
      target: DuelStateFacade.Card | DuelStateFacade.Location,
      from: DuelStateFacade.Card | DuelStateFacade.Location
    ) => {
      const fromLocation = Util.toLocation(from),
        fromIndex = fromLocation.fieldIndex;
      const targetLocation = Util.toLocation(target),
        targetIndex = targetLocation.fieldIndex;
      assert(fromLocation.isField, "Card is not in play");
      assert(targetLocation.isField, "Target is not in play");
      assert(fromIndex === targetIndex, `Target is not on the same lane (${fromIndex} != ${targetIndex})`);
      assert(fromLocation.owner !== targetLocation.owner, `Target is not on the opponent's side of the board`);
    },
    isFriendly: (
      a: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player,
      b: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player
    ) => {
      const aPlayer = Util.toPlayer(a);
      const bPlayer = Util.toPlayer(b);
      assert(aPlayer === bPlayer, "Must be on the friendly side of the board");
    },
    isEnemy: (
      a: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player,
      b: DuelStateFacade.Card | DuelStateFacade.Location | DuelStateFacade.Player
    ) => {
      const aPlayer = Util.toPlayer(a);
      const bPlayer = Util.toPlayer(b);
      assert(aPlayer !== bPlayer, "Must be on the enemy's side of the board");
    },

    isInHand: (card: DuelStateFacade.Card) => {
      assert(card.location.isHand, "Card must be in your hand");
    },
    isUnitType: (card: DuelStateFacade.Card) => {
      assert(card.isUnit, "Card must be unit-type.");
    },
    isTrapType: (card: DuelStateFacade.Card) => {
      assert(card.isTrap, "Card must be trap-type.");
    },
    isField: (to: DuelStateFacade.Location) => {
      assert(to.isField, "Location must be a field.");
    },
    isUnitField: (to: DuelStateFacade.Location) => {
      assert(to.isUnitField, "Destination must be an empty unit-field.");
    },
    isTrapField: (to: DuelStateFacade.Location) => {
      assert(to.isTrapField, "Destination must be a trap-field.");
    },

    isUnoccupied: (location: DuelStateFacade.Location) => assert(!location.hasCards, `${location.id} is occupied`),
    isEmpty: (location: DuelStateFacade.Location) => assert(!location.hasCards, `${location.id} is not empty`),
    hasCards: (location: DuelStateFacade.Location) => assert(location.hasCards, `${location.id} is empty`),

    canAffordCommand: (command: PlayerCommand) => {
      if (cheats?.noActionPointChecks) return;
      assert(command.player.actionPoints >= command.cost, "Not enough action points");
    },
  };

  return assertFunctions;
}
