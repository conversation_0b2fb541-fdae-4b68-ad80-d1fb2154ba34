import { DuelStateContext, createDuelContext } from '@drimgar/dueling/src/DuelStateContext';
import { createInitialDuelStateData } from '@drimgar/dueling/src/createInitialDuelStateData';
import { DuelStateData } from '@drimgar/dueling/src/data/DuelStateData';
import { Keyword, PlayerNumber } from '@drimgar/dueling/src/data/primitives';
import { CommandFactory } from '@drimgar/dueling/src/gameplay/commands';
import { createDuelGameplayController } from '@drimgar/dueling/src/gameplay/gameplay';
import { createEventHistoryController } from '@drimgar/dueling/src/gameplay/operation-events';
import { Operations } from '@drimgar/dueling/src/gameplay/operations';
import { CallbackList } from '@drimgar/dueling/src/util/CallbackList';

import { DuelPrinter } from './lib/printer';
import { getProcessArg } from './lib/process-args';

const delayBetweenEventsMs = +getProcessArg('pause', 'p', '8');

let DRAW_ON_SCREEN = true;
// DRAW_ON_SCREEN = false;

const duelConfiguration = {
  settings: {
    initialActionPointsFirst: 5,
    initialActionPointsSecond: 5,
  },
  features: {
    skipDeckShuffling: true,
  },
};

const printerSettings: Parameters<typeof DuelPrinter.printDuelStateOnScreen>[1] = {
  // hideFaceDownCards: false,
  // condensedDeck: false,
};

function attachPrinter(data: DuelStateData.FullState, getDelayMs: () => number) {
  const lastErrors = [] as Error[];
  function handleOperationError(error: Error) {
    lastErrors.push(error);
    console.error(error);
  }

  const onOperationStatusChangeHandlers = new CallbackList<
    (operation: Operations.OperationInstance<any>) => void
  >();

  onOperationStatusChangeHandlers.add(operation => {
    if (operation.state.error) handleOperationError(operation.state.error);
  });

  const debugLines = [] as string[];
  console.debug = (...args: any[]) => debugLines.push(args.join(' '));

  const events = createEventHistoryController();
  onOperationStatusChangeHandlers.add(operation => {
    if (events.getEventHistory().length > 0) {
      if (operation.parent === null && operation.state.status === 'began') {
        events.appendNull();
      }
    }
    events.appendEventFromOperationState(operation);
  });

  //// Print to console

  function redrawScreen() {
    console.clear();
    DuelPrinter.printDuelStateOnScreen(data, printerSettings);
    console.log();
    console.log(data.activeModifiers, data.turn.actionsMade);
    console.log();
    DuelPrinter.printErrorsOnScreen(lastErrors);
    console.log();
    if (debugLines.length) console.log(debugLines.join('\n'));
    else DuelPrinter.printDuelEventsOnScreen(events);
    console.log();
  }

  onOperationStatusChangeHandlers.add(() => {
    if (DRAW_ON_SCREEN) redrawScreen();
    DuelPrinter.pause(getDelayMs());
  });

  return {
    events,
    redrawScreen,
    onOperationStatusChange(operation: Operations.OperationInstance<any>) {
      return onOperationStatusChangeHandlers.call(operation);
    },
  };
}

function populateDecks(data: DuelStateData.FullState) {
  const gameplay = createDuelGameplayController(data, duelConfiguration);

  return gameplay.prepareBoard(
    [
      {
        type: DuelStateData.CardType.Trap,
        effects: [
          {
            type: 'active',
            operationType: 'DeployUnit',
            operationStatus: 'ended',
            locationTypes: ['field'],
            condition: `event.card.instanceId === get.opposingUnit(card)?.instanceId`,
            implementation: `op.kill(event.card)`,
          },
        ],
      },
      {
        basePower: 0,
        effects: [
          {
            type: 'passive',
            locationTypes: ['field', 'grave', 'hand'],
            condition: `true`,
            implementation: `pass.raisePower(card, card.controller.opponent.hand.cardsCount)`,
          },
        ],
      },
      { basePower: 2 },
      { basePower: 3 },
      { basePower: 4 },
      { basePower: 5 },
      { basePower: 6 },
      { basePower: 7 },
      { basePower: 8 },
      { basePower: 9 },
    ],
    [
      { basePower: 1, keywords: [Keyword.RUSH] },
      {
        basePower: 2,
        effects: [
          {
            type: 'flip',
            manner: 'combat',
            implementation: `op.kill(get.opposingUnit(card))`,
          },
        ],
        keywords: [Keyword.SNEAK],
      },
      { basePower: 3, baseStars: 1 },
      { basePower: 4 },
      { basePower: 5 },
      { basePower: 6 },
      { basePower: 7 },
      { basePower: 8 },
      { basePower: 9 },
    ]
  );
}

function createStatelessDuelRunner(
  data: DuelStateData.FullState,
  onOperationStateChange?: (operation: any) => void
) {
  function doSomethingWithTempGameplayControllerInstance(
    doTheThing: (gameplay: ReturnType<typeof createDuelGameplayController>) => void
  ) {
    const gameplay = createDuelGameplayController(data, duelConfiguration);

    if (onOperationStateChange) {
      gameplay.onOperationStatusChangeHandlers.add(onOperationStateChange);
    }

    return doTheThing(gameplay);
  }

  function startMatch(firstPlayerNumber: PlayerNumber) {
    return doSomethingWithTempGameplayControllerInstance(g => g.startMatch(firstPlayerNumber));
  }

  function submitCommand<T extends keyof CommandFactory>(
    commandType: T,
    ...args: Parameters<CommandFactory[T]>
  ) {
    return doSomethingWithTempGameplayControllerInstance(g =>
      g.submitCommand(commandType, ...args)
    );
  }

  return {
    startMatch,
    submitCommand,
  };
}

function createScenariosFactory(
  context: DuelStateContext,
  clearEventList: () => void,
  onOperationStateChange?: (operation: any) => void
) {
  async function doEndTurnLoop() {
    const { data } = context;

    while (true) {
      await DuelPrinter.pauseUntilKeypress();
      runner.submitCommand('endturn', data.turn.playerNumber!);
    }
  }

  const runner = createStatelessDuelRunner(context.data, onOperationStateChange);

  return {
    async runExperiment0() {
      await doEndTurnLoop();
    },

    async runExperiment1() {
      const { facade } = context;
      const p1 = facade.players[1];
      const p2 = facade.players[2];
      const cTrap = p1.hand.cards[0];
      const cAtak = p1.hand.cards[1];

      // gameplay.submitCommand("draw", p1);
      // gameplay.submitCommand("deploy", p1, p1.hand.cards[3], "1-fu2");
      // await DuelPrinter.pauseUntilKeypress();

      runner.submitCommand('deploy', 1, cAtak.instanceId, '1-fu2');
      // gameplay.submitCommand("deploy", p1, cTrap, "1-fu1"); // Error: Destination must be a trap-field.
      runner.submitCommand('deploy', 1, cTrap.instanceId, '1-ft1');

      // await DuelPrinter.pauseUntilKeypress();

      // gameplay.submitCommand("attack", p1, cAtak); // Error: Unit already performed action(s) of type deploy
      // gameplay.submitCommand("draw", p1); // Error: Not enough action points
      runner.submitCommand('endturn', 1);

      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu1');

      // await DuelPrinter.pauseUntilKeypress();

      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu2');
      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu3');
      // gameplay.submitCommand("deploy", p2, p2.hand.cards[0], "2-fu4");
      // gameplay.submitCommand("deploy", p2, p2.hand.cards[0], "2-fu5");
      runner.submitCommand('endturn', 2);
      runner.submitCommand('attack', 1, cAtak.instanceId);

      // await doEndTurnLoop();

      // await DuelPrinter.pauseUntilKeypress();
      // delayBetweenEventsMs = 200;
      // gameplay.submitCommand("draw", p2);
      // gameplay.submitCommand("draw", p2);
    },

    async runExperiment2() {
      const { facade } = context;
      const p1 = facade.players[1];
      const p2 = facade.players[2];

      runner.submitCommand('draw', 1);

      const cAtk = p1.hand.cards[3];
      const cDef = p2.hand.cards[1];

      // console.debug(JSON.stringify(cDef.model, null, 2));

      runner.submitCommand('deploy', 1, cAtk.instanceId, '1-fu2');
      runner.submitCommand('endturn', 1);

      runner.submitCommand('deploy', 2, cDef.instanceId, '2-fu2');
      runner.submitCommand('endturn', 2);

      await DuelPrinter.pauseUntilKeypress();

      clearEventList();

      runner.submitCommand('attack', 1, cAtk.instanceId); // Error: Unit already performed action(s) of type deploy

      await DuelPrinter.pauseUntilKeypress();

      // gameplay.submitCommand("deploy", p1, cAtak, "1-fu2");
      // gameplay.submitCommand("deploy", p1, cTrap, "1-fu1"); // Error: Destination must be a trap-field.
      // gameplay.submitCommand("deploy", p2, cTrap, "1-ft1");

      // await DuelPrinter.pauseUntilKeypress();

      // gameplay.submitCommand("draw", p1); // Error: Not enough action points

      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu1');

      // await DuelPrinter.pauseUntilKeypress();

      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu2');
      runner.submitCommand('deploy', 2, p2.hand.cards[0].instanceId, '2-fu3');
      // gameplay.submitCommand("deploy", p2, p2.hand.cards[0], "2-fu4");
      // gameplay.submitCommand("deploy", p2, p2.hand.cards[0], "2-fu5");
      // gameplay.submitCommand("attack", p1, cAtak);

      // await doEndTurnLoop();

      // await DuelPrinter.pauseUntilKeypress();
      // delayBetweenEventsMs = 200;
      // gameplay.submitCommand("draw", p2);
      // gameplay.submitCommand("draw", p2);
    },

    async runExperiment3() {
      const { facade } = context;
      const p1 = facade.players[1];
      const p2 = facade.players[2];

      runner.submitCommand('endturn', 1);

      runner.submitCommand('draw', 2);
      runner.submitCommand('draw', 2);

      // await DuelPrinter.pauseUntilKeypress();

      runner.submitCommand('endturn', 2);

      runner.submitCommand('deploy', 1, p1.hand.topCard!.instanceId, '1-fu2');
      runner.submitCommand('deploy', 1, p1.hand.topCard!.instanceId, '1-fu3');
      // runner.submitCommand("deploy", 1, p1.hand.topCard!.instanceId, "1-ft3");

      // runner.submitCommand("endturn", 1);
      // runner.submitCommand("endturn", 2);
      // runner.submitCommand("endturn", 1);
      // runner.submitCommand("endturn", 2);
      // runner.submitCommand("endturn", 1);
      // runner.submitCommand("endturn", 2);
    },

    async runExperiment4() {
      const { facade } = context;
      const p1 = facade.players[1];
      const p2 = facade.players[2];

      runner.submitCommand('endturn', 1);

      const tribute = p2.hand.cards[0];
      runner.submitCommand('deploy', 2, tribute.instanceId, '2-fu2');

      const grand = p2.hand.cards[1];
      runner.submitCommand('deploy', 2, grand.instanceId, '2-fu2');
    },
  } satisfies Record<string, () => unknown>;
}

async function go() {
  while (true) {
    console.clear();

    const data = createInitialDuelStateData();
    const context = createDuelContext(data, duelConfiguration);

    populateDecks(data);

    const printer = attachPrinter(data, () => delayBetweenEventsMs);

    const runner = createStatelessDuelRunner(context.data, op =>
      printer.onOperationStatusChange(op)
    );
    runner.startMatch(1);

    printer.redrawScreen();

    ////    //// //// ////
    //// ////    //// //// ////
    //// //// ////    //// //// ////

    const scenarios = createScenariosFactory(
      context,
      () => printer.events.clear(),
      op => printer.onOperationStatusChange(op)
    );
    // await scenarios.runExperiment0();
    // await scenarios.runExperiment1();
    // await scenarios.runExperiment2();
    // await scenarios.runExperiment3();
    await scenarios.runExperiment4();
    // await scenarios.runExperiment5();

    //// //// ////    //// //// ////
    //// ////    //// //// ////
    ////    //// ////

    const key = await DuelPrinter.pauseUntilKeypress();
    if (key === 'q') break;
  }
}

go();
