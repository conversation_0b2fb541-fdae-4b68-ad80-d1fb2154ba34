import { PlayerNumber } from "./primitives";

export type LocationId = (typeof LocationId.ALL)[number];

export const LANES_COUNT = 5;
export const LANE_NUMBERS = Array.from({ length: LANES_COUNT }, (_, i) => i + 1) as readonly FieldIndex[];

export type FieldIndex = IntegersUpTo9<typeof LANES_COUNT>;
export type LocationIdSuffix = "hnd" | "dck" | "grv" | `fu${FieldIndex}` | `ft${FieldIndex}` | "bys";

export module LocationId {
  type PlayerNum = PlayerNumber;

  type UnitField = `${PlayerNum}-fu${FieldIndex}`;
  type TrapField = `${PlayerNum}-ft${FieldIndex}`;
  type Field = UnitField | TrapField;
  type Hand = `${PlayerNum}-hnd`;
  type Deck = `${PlayerNum}-dck`;
  type Grave = `${PlayerNum}-grv`;

  type ExtractSuffix<T extends string> = T extends `${string}-${infer S}` ? S : never;
  type ExtractPlayerNum<T extends string> = T extends `${infer S}-${string}` ? S : never;

  export type Suffix = (typeof Suffix.ALL)[number];

  export module Suffix {
    const unitKeys = Array.from({ length: LANES_COUNT }, (_, i) => `fu${i + 1}` as LocationIdSuffix);
    const trapKeys = Array.from({ length: LANES_COUNT }, (_, i) => `ft${i + 1}` as LocationIdSuffix);
    export const ALL = ["hnd", "dck", "grv", ...unitKeys, ...trapKeys, "bys"] as LocationIdSuffix[];
    // export const ALL = ["hnd", "dck", "grv", "fu1", "fu2", "fu3", "fu4", "ft1", "ft2", "ft3", "ft4", "bys"] as const;

    export function isUnitField(suffix: Suffix) {
      return /fu\d$/.test(suffix);
    }

    export function isTrapField(suffix: Suffix) {
      return /ft\d$/.test(suffix);
    }

    export function isField(suffix: Suffix) {
      return isUnitField(suffix) || isTrapField(suffix);
    }
  }

  export const ALL = [
    ...Suffix.ALL.map(suffix => `1-${suffix}` as const),
    ...Suffix.ALL.map(suffix => `2-${suffix}` as const),
  ] as const;

  //// FAQ: Type ////

  export function isUnitField(lotId: LocationId): lotId is UnitField {
    return /-fu\d$/.test(lotId);
  }

  export function isTrapField(lotId: LocationId): lotId is TrapField {
    return /-ft\d$/.test(lotId);
  }

  export function isField(lotId: LocationId): lotId is Field {
    return isUnitField(lotId) || isTrapField(lotId);
  }

  export function isHand(lotId: LocationId): lotId is Hand {
    return /hnd$/.test(lotId);
  }

  export function isDeck(lotId: LocationId): lotId is Deck {
    return /dck$/.test(lotId);
  }

  export function isGraveyard(lotId: LocationId): lotId is Grave {
    return /grv$/.test(lotId);
  }

  export function isAbyss(lotId: LocationId): lotId is Grave {
    return /bys$/.test(lotId);
  }

  export function isOwnedByPlayerNum<TPlayerNum extends PlayerNum>(
    lotId: LocationId,
    playerNum: TPlayerNum
  ): lotId is `${TPlayerNum}-${Suffix}` {
    return lotId[0] === String(playerNum);
  }

  //// Extract ////

  export function extractSuffix<T extends LocationId>(lotId: T) {
    return lotId.slice(2) as ExtractSuffix<T>;
  }

  export function extractPlayerNum<T extends LocationId>(lotId: T) {
    const playerNumStr = lotId[0] as ExtractPlayerNum<T>;
    return parseInt(playerNumStr) as PlayerNum;
  }

  export function extractFieldNumber<T extends LocationId>(lotId: T) {
    if (!isField(lotId)) throw new Error(`Not a field lot: ${lotId}`);
    return parseInt(lotId[lotId.length - 1]) as FieldIndex;
  }

  //// Ref ////

  export function getOpposingPlayerNum(lotId: LocationId): PlayerNum {
    return extractPlayerNum(lotId) === 1 ? 2 : 1;
  }

  export function getOpposingFieldId<T extends LocationId>(lotId: T) {
    return `${getOpposingPlayerNum(lotId)}-${extractSuffix(lotId)}` as Field;
  }

  export function getOpposingUnitFieldId(lotId: LocationId) {
    return `${getOpposingPlayerNum(lotId)}-fu${extractFieldNumber(lotId)}` as UnitField;
  }

  export function getOpposingTrapFieldId(lotId: LocationId) {
    return `${getOpposingPlayerNum(lotId)}-ft${extractFieldNumber(lotId)}` as TrapField;
  }
}

export function makeAllPossibleLocationIds(lanesCount: number) {
  const staticSuffixes: LocationIdSuffix[] = ["hnd", "dck", "grv", "bys"];
  const dynamicSuffixes = ["fu", "ft"].flatMap(prefix =>
    Array.from({ length: lanesCount }, (_, i) => (prefix + (i + 1)) as LocationIdSuffix)
  );
  const allSuffixes = [...staticSuffixes, ...dynamicSuffixes];

  const allPlayerNums = [1, 2] as PlayerNumber[];
  const allLocationIds = allPlayerNums.flatMap(playerNum =>
    allSuffixes.map(suffix => `${playerNum}-${suffix}` as LocationId)
  );

  return allLocationIds;
}

type IntegersUpTo9<T> = //
  T extends 1
    ? 1
    : T extends 2
    ? 1 | 2
    : T extends 3
    ? 1 | 2 | 3
    : T extends 4
    ? 1 | 2 | 3 | 4
    : T extends 5
    ? 1 | 2 | 3 | 4 | 5
    : T extends 6
    ? 1 | 2 | 3 | 4 | 5 | 6
    : T extends 7
    ? 1 | 2 | 3 | 4 | 5 | 6 | 7
    : T extends 8
    ? 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8
    : T extends 9
    ? 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
    : never; // Extend this pattern as needed
