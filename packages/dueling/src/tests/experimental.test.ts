import { createDuelContext } from "../DuelStateContext";
import { createInitialDuelStateData } from "../createInitialDuelStateData";

import type { LocationId } from "../data/primitives";
import type { DuelStateData } from "../data/DuelStateData";

describe("Avoid Cyclical Power Calculation", () => {
  const data = createInitialDuelStateData();
  const context = createDuelContext(data);
  const { facade, mutators } = context;

  const createCard = (model: Partial<DuelStateData.CardModel>, lotId: LocationId) => {
    const ciid = mutators.createCard(model, lotId);
    const card = facade.cards[ciid];
    if (!card) throw new Error("Card not found");
    return card;
  };

  const cardA = createCard({ basePower: 4 }, "1-fu1");
  const cardB = createCard({ basePower: 6 }, "2-fu1");
  const cardC = createCard({ basePower: 1 }, "2-fu1");

  function logPowers() {
    // console.log(`
    // cardA.power: ${cardA.power}, 
    // cardB.power: ${cardB.power}, 
    // cardC.power: ${cardC.power}`);
  }

  it("card.power should return correct base power", () => {
    logPowers();

    mutators.addModifier({ cardInstanceId: cardC.instanceId, powerOffset: () => cardA.power + cardB.power });
    mutators.addModifier({ cardInstanceId: cardA.instanceId, powerOffset: () => cardB.power });
    mutators.addModifier({ cardInstanceId: cardB.instanceId, powerOffset: () => cardA.power });

    logPowers();
  });
});
