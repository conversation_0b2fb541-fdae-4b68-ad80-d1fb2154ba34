import { OperationEvent } from "../gameplay/operation-events";
import { Operations } from "../gameplay/operations";

type FlattenIfArray<T> = T extends (infer R)[] ? R : T;
type ValueTypes<T> = T extends { [k: string]: infer U } ? FlattenIfArray<U> : never;
type UnionValues<T> = ValueTypes<T[keyof T]>;
type PropValue = UnionValues<Operations.OperationPropsPerType>;

function serializeThing($value: any) {
  if (typeof $value === "string") {
    return $value;
  } else if (typeof $value === "number" || typeof $value === "boolean" || $value == null) {
    return $value;
  } else if ("id" in $value) {
    return $value.id;
  } else if ("instanceId" in $value) {
    return $value.instanceId;
  } else if ("playerNumber" in $value) {
    return $value.playerNumber;
  } else {
    return undefined;
  }
}

export function serializeOperationEvent<TProps extends OperationEvent>(props: TProps) {
  const result = {} as any;
  for (const [key, value] of Object.entries(props)) {
    if (key === "props") continue;

    const $value = value as PropValue;

    if (key === "parentTypes") {
      result[key] = $value;
    } else if (key === "error") {
      result[key] = String($value);
    } else if (key === "command") {
      Object.assign(result, serializeOperationEvent($value));
    } else if (Array.isArray($value)) {
      result[key] = $value.map(serializeThing) as any[];
    } else {
      const serializedValue = serializeThing($value);
      if (serializedValue !== undefined) {
        result[key] = serializedValue;
      }
    }
  }
  return result;
}
