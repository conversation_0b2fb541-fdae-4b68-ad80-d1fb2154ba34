import { DuelStateContext } from "../DuelStateContext";
import { DuelStateFacade } from "../facade/DuelStateFacade";
import { Operations } from "./operations";
import { CardInstanceId, FlipManner, LocationId, PlayerNumber } from "../data/primitives";

export type PlayerCommand = {
  type: CommandType;
  cost: number;
  player: DuelStateFacade.Player;
  runCommand: (
    runChildOperation: <K extends Operations.OperationType>(
      operationType: K,
      props: Operations.OperationPropsPerType[K]
    ) => Operations.OperationInstance<K>
  ) => void;
  assertCommandIsPossible: () => void;
  parameters: any;

  toString?: () => string;
};

export function createCommandFactory(context: DuelStateContext) {
  const { assert, configuration, facade, getters } = context;

  const withFacades = {
    draw(player: DuelStateFacade.Player): PlayerCommand {
      return {
        type: "draw",
        cost: 1,
        player,
        runCommand: runChildOperation => {
          runChildOperation("DrawCard", { player, count: 1 });
        },
        assertCommandIsPossible: () => {
          assert.custom(player.deck.hasCards, "Deck empty");
        },
        parameters: {},
      };
    },
    deploy(player: DuelStateFacade.Player, card: DuelStateFacade.Card, to: DuelStateFacade.Location): PlayerCommand {
      return {
        type: "deploy",
        cost: card.isUnit ? configuration.features.unitCost : card.isTrap ? configuration.features.trapCost : 0,
        player,
        runCommand: runChildOperation => {
          if (card.isTrap) return runChildOperation("DeployTrap", { card, to, isManual: true });
          if (card.isUnit) {
            const useTributeDeploy = getters.requiresTribute(card);
            if (useTributeDeploy) return runChildOperation("DeployUnitWithTribute", { card, to, isManual: true });
            else return runChildOperation("DeployUnit", { card, to, isManual: true });
          }
          throw new Error("Invalid card type.");
        },
        assertCommandIsPossible: () => {
          assert.custom(card.location === player.hand, "Card is not in player's hand.");
          assert.canManuallyDeployTo(card, to);
        },
        parameters: {},
      };
    },
    relocate(player: DuelStateFacade.Player, card: DuelStateFacade.Card, to: DuelStateFacade.Location): PlayerCommand {
      return {
        type: "relocate",
        cost: 0,
        player,
        runCommand: runChildOperation => {
          runChildOperation("UnitRelocate", { card, to, isManual: true });
        },
        assertCommandIsPossible: () => {
          assert.custom(card.isUnit, "You can only relocate UNIT type cards.");
          assert.custom(card.location.isField, "Unit card is not in play.");
          assert.custom(to.isField, "You can only relocate to a field location.");
          assert.custom(to.owner === player, "You can only relocate to fields on your side of the board.");
          assert.custom(card.location !== to, "You cannot relocate a card to its current location.");
          assert.canManuallyRelocateTo(card, to);
        },
        parameters: {},
      };
    },
    attack(player: DuelStateFacade.Player, card: DuelStateFacade.Card): PlayerCommand {
      return {
        type: "attack",
        cost: 0,
        player,
        runCommand: runChildOperation => {
          runChildOperation("UnitAttack", { card, isManual: true });
        },
        assertCommandIsPossible: () => {
          assert.custom(card.isUnit, "You can only attack with UNIT type cards.");
          assert.custom(card.location.isField, "Unit card is not in play.");
          assert.canManuallyAttack(card);
        },
        parameters: {},
      };
    },
    reveal(player: DuelStateFacade.Player, card: DuelStateFacade.Card): PlayerCommand {
      return {
        type: "reveal",
        cost: 0,
        player,
        runCommand: runChildOperation => {
          runChildOperation("UnitReveal", { card, manner: FlipManner.Manual, isManual: true });
        },
        assertCommandIsPossible: () => {
          assert.custom(card.isUnit, "You can only reveal UNIT type cards.");
          assert.custom(card.location.isField, "Unit card is not in play.");
          assert.custom(card.isFaceDown, "Unit card is already face up.");
          assert.canManuallyReveal(card);
        },
        parameters: {},
      };
    },
    endturn(player: DuelStateFacade.Player): PlayerCommand {
      return {
        type: "endturn",
        cost: 0,
        player,
        runCommand: runChildOperation => {
          runChildOperation("EndTurn", { player, turnNumber: facade.turn.turnNumber, isManual: true });
        },
        assertCommandIsPossible: () => {
          assert.custom(player.playerNumber === facade.turn.player?.playerNumber, "It is not your turn.");
        },
        parameters: {},
      };
    },
    surrender(player: DuelStateFacade.Player): PlayerCommand {
      return {
        type: "surrender",
        cost: 0,
        player,
        runCommand: runChildOperation => {
          runChildOperation("Surrender", { player });
        },
        assertCommandIsPossible: () => {
          assert.custom(player === facade.turn.player, "It is not your turn.");
        },
        parameters: {},
      };
    },
  } satisfies Record<CommandType, (...args: any[]) => PlayerCommand>;

  const withoutFacades = {
    draw(player: PlayerNumber): PlayerCommand {
      return withFacades.draw(facade.players[player]);
    },
    deploy(player: PlayerNumber, card: CardInstanceId, to: LocationId): PlayerCommand {
      return withFacades.deploy(facade.players[player], facade.cards[card], facade.locations[to]);
    },
    relocate(player: PlayerNumber, card: CardInstanceId, to: LocationId): PlayerCommand {
      return withFacades.relocate(facade.players[player], facade.cards[card], facade.locations[to]);
    },
    attack(player: PlayerNumber, card: CardInstanceId): PlayerCommand {
      return withFacades.attack(facade.players[player], facade.cards[card]);
    },
    reveal(player: PlayerNumber, card: CardInstanceId): PlayerCommand {
      return withFacades.reveal(facade.players[player], facade.cards[card]);
    },
    endturn(player: PlayerNumber): PlayerCommand {
      return withFacades.endturn(facade.players[player]);
    },
    surrender(player: PlayerNumber): PlayerCommand {
      return withFacades.endturn(facade.players[player]);
    },
  } satisfies { [K in CommandType]: (...args: any[]) => PlayerCommand };

  return withoutFacades;
}

type CommandType = "draw" | "deploy" | "relocate" | "attack" | "reveal" | "endturn" | "surrender";

export type CommandFactory = ReturnType<typeof createCommandFactory>;
