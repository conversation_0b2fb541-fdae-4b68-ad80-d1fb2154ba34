import { DuelStateData } from "../data/DuelStateData";
import { LocationId } from "../data/LocationId";
import { DuelStateContext } from "../DuelStateContext";
import { DuelStateFacade } from "../facade/DuelStateFacade";
import { Operations } from "./operations";
import { FlipManner, Keyword } from "../data/primitives";

export module CardEffects {
  function executeScript<T extends {}>(functionBody: string, ctx: T) {
    const fn = new Function(...Object.keys(ctx), `return eval(\`${functionBody}\`)`);
    return fn(...Object.values(ctx));
  }

  export function isLocationOfType(location: DuelStateFacade.Location, allowedTypes: ("field" | "hand" | "grave")[]) {
    return allowedTypes.some(type => {
      switch (type) {
        case "field":
          return LocationId.isField(location.id);
        case "hand":
          return LocationId.isHand(location.id);
        case "grave":
          return LocationId.isGraveyard(location.id);
        default:
          throw new Error("Invalid location type");
      }
    });
  }

  export function* iterateOverCardsEligibleForEffectActivation({ facade }: DuelStateContext) {
    function* iterateOverPlayers() {
      yield facade.turn.player ?? facade.players[1];
      yield facade.turn.player?.opponent ?? facade.players[2];
    }

    function* iterateOverCardsEligibleForEffectActivation(player: DuelStateFacade.Player) {
      yield* player.units;
      yield* player.traps;
      if (player.grave.topCard) yield player.grave.topCard;
      if (player.deck.topCard) yield player.deck.topCard;
      yield* player.hand.cards;
    }

    for (const player of iterateOverPlayers()) {
      yield* iterateOverCardsEligibleForEffectActivation(player);
    }
  }

  function createPassiveMethodsProvider({ mutators }: DuelStateContext) {
    return {
      raisePower(card: DuelStateFacade.Card, amount: number) {
        if (!card) throw new Error("Card not found");
        if (!card.instanceId) throw new Error("Card has no instanceId");
        mutators.addModifier({ cardInstanceId: card.instanceId, powerOffset: amount, ephemeral: true });
      },
      lowerPower(card: DuelStateFacade.Card, amount: number) {
        if (!card) throw new Error("Card not found");
        if (!card.instanceId) throw new Error("Card has no instanceId");
        mutators.addModifier({ cardInstanceId: card.instanceId, powerOffset: -amount, ephemeral: true });
      },
      addKeyword(card: DuelStateFacade.Card, keyword: Keyword) {
        if (!card) throw new Error("Card not found");
        if (!card.instanceId) throw new Error("Card has no instanceId");
        mutators.addModifier({ cardInstanceId: card.instanceId, keywordsAdded: [keyword], ephemeral: true });
      },
      removeKeyword(card: DuelStateFacade.Card, keyword: Keyword) {
        if (!card) throw new Error("Card not found");
        if (!card.instanceId) throw new Error("Card has no instanceId");
        mutators.addModifier({ cardInstanceId: card.instanceId, keywordsRemoved: [keyword], ephemeral: true });
      },
      setFaceUp(card: DuelStateFacade.Card, value: boolean) {
        if (!card) throw new Error("Card not found");
        if (!card.instanceId) throw new Error("Card has no instanceId");
        mutators.addModifier({ cardInstanceId: card.instanceId, faceUp: value, ephemeral: true });
      },
    };
  }

  function createOperationMethodsProvider({ mutators }: DuelStateContext, runOperation: RunOperationFunction) {
    return {
      endTurn(player: DuelStateFacade.Player) {
        runOperation("EndTurn", { player, isManual: false, turnNumber: 0 }); // turnNumber needs to be provided
      },
      draw(player: DuelStateFacade.Player) {
        runOperation("DrawCard", { player, count: 1 });
      },
      discard(card: DuelStateFacade.Card) {
        runOperation("DiscardCard", { card });
      },
      addToHand(card: DuelStateFacade.Card) {
        runOperation("AddCardToHand", { card });
      },
      addToDeck(card: DuelStateFacade.Card) {
        runOperation("AddCardToDeck", { card });
      },
      kill(card: DuelStateFacade.Card) {
        runOperation("DestroyUnit", { card, death_type: "effect-kill" });
      },
      dispose(card: DuelStateFacade.Card) {
        runOperation("DestroyTrap", { card });
      },
      sacrifice(card: DuelStateFacade.Card) {
        runOperation("DestroyUnit", { card, death_type: "effect-sacrifice" });
      },
      damage(player: DuelStateFacade.Player, amount: number) {
        runOperation("DamagePlayer", { player, amount });
      },
      heal(player: DuelStateFacade.Player, amount: number) {
        runOperation("HealPlayer", { player, amount });
      },
      changeAP(player: DuelStateFacade.Player, amount: number) {
        runOperation("ChangePlayerActionPoints", { player, offset: amount });
      },
      deploy(card: DuelStateFacade.Card, to: DuelStateFacade.Location) {
        runOperation("DeployUnit", { card, to, isManual: false });
      },
      forceAttack(card: DuelStateFacade.Card) {
        runOperation("UnitAttack", { card, isManual: false });
      },
      forceManualAttack(card: DuelStateFacade.Card) {
        runOperation("UnitAttack", { card, isManual: true });
      },
      forceRelocate(card: DuelStateFacade.Card, to: DuelStateFacade.Location) {
        runOperation("UnitRelocate", { card, to, isManual: false });
      },
      forceManualRelocate(card: DuelStateFacade.Card, to: DuelStateFacade.Location) {
        runOperation("UnitRelocate", { card, to, isManual: true });
      },
      forceSwap(cardA: DuelStateFacade.Card, cardB: DuelStateFacade.Card) {
        runOperation("SwapCardLocations", { cardA, cardB });
      },
      reveal(card: DuelStateFacade.Card) {
        runOperation("UnitReveal", { card, manner: FlipManner.Silent, isManual: false });
      },
      flipFaceDown(card: DuelStateFacade.Card) {
        runOperation("ChangeCardFaceUp", { card, faceUp: false });
      },
      flipFaceUp(card: DuelStateFacade.Card) {
        runOperation("ChangeCardFaceUp", { card, faceUp: true });
      },
      display(cards: DuelStateFacade.Card[]) {
        runOperation("DisplayCards", { cards });
      },
      moveTo(card: DuelStateFacade.Card, to: DuelStateFacade.Location) {
        runOperation("ChangeCardLocation", { card, to });
      },
      assureFaceUp(card: DuelStateFacade.Card) {
        if (card.isFaceUp) return;
        if (card.isUnit && card.isInPlay) {
          runOperation("UnitReveal", { card, manner: FlipManner.Silent, isManual: false });
        } else {
          runOperation("ChangeCardFaceUp", { card, faceUp: true });
        }
      },
      addTempFx(target: DuelStateFacade.Card, modifier: Omit<DuelStateData.ActiveModifier, "cardInstanceId">) {
        mutators.addModifier({
          cardInstanceId: target.instanceId,
          expiry: () => target.isInPlay === false,
          ...modifier,
        });
      },
      clearActions(card: DuelStateFacade.Card) {
        mutators.forgetUnitActionsThisTurn(card.instanceId);
      },
      spawnToken(to: DuelStateFacade.Location, slug = "token", faceup = true) {
        runOperation("SpawnToken", { at: to, slug, faceUp: faceup });
      },
    };
  }

  export function createPassiveEffectsController(context: DuelStateContext) {
    type PassiveCardEffect = DuelStateData.CardEffect & { readonly type: "passive" };

    const pass = createPassiveMethodsProvider(context);

    function makePassiveEffectContext(card: DuelStateFacade.Card) {
      return { card, pass, get: context.getters };
    }

    return {
      runConditionScript(card: DuelStateFacade.Card, $effect: PassiveCardEffect) {
        const ctx = makePassiveEffectContext(card);
        const result = executeScript($effect.condition, ctx);
        return Boolean(result);
      },
      runImplementationScript(card: DuelStateFacade.Card, $effect: PassiveCardEffect) {
        const ctx = makePassiveEffectContext(card);
        executeScript($effect.implementation, ctx);
      },
    };
  }

  export function createActiveEffectsController(context: DuelStateContext, runOperation: RunOperationFunction) {
    type ActiveCardEffect = DuelStateData.CardEffect & { readonly type: "active" };

    const op = createOperationMethodsProvider(context, runOperation);

    function makeActiveEffectContext(card: DuelStateFacade.Card, triggeringOperationProps: any) {
      return { card, event: triggeringOperationProps, op, get: context.getters };
    }

    return {
      assertActiveEffectIsPossible(
        card: DuelStateFacade.Card,
        $effect: ActiveCardEffect,
        triggeringOperation: Operations.OperationInstance<any>
      ) {
        const isCardLocationAllowed = CardEffects.isLocationOfType(card.location, $effect.locationTypes);
        context.assert.custom(isCardLocationAllowed, "Card location is not allowed");

        const conditionMet = this.runConditionScript(card, $effect, triggeringOperation);
        context.assert.custom(conditionMet, "Condition script no longer truthy");
      },
      runConditionScript(
        card: DuelStateFacade.Card,
        $effect: ActiveCardEffect,
        triggeringOperation: Operations.OperationInstance<any>
      ) {
        const ctx = makeActiveEffectContext(card, triggeringOperation.props);
        const result = executeScript($effect.condition, ctx);
        return Boolean(result);
      },
      runImplementationScript(
        card: DuelStateFacade.Card,
        $effect: ActiveCardEffect,
        triggeringOperation: Operations.OperationInstance<any>
      ) {
        const ctx = makeActiveEffectContext(card, triggeringOperation.props);
        executeScript($effect.implementation, ctx);
      },
    };
  }
}

type RunOperationFunction = <K extends Operations.OperationType>(
  operationType: K,
  props: Operations.OperationPropsPerType[K]
) => Operations.OperationInstance<K>;
