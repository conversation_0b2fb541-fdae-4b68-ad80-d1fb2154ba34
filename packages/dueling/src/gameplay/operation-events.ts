import { InstanceToId } from "../serialization/InstanceToId";
import type { Operations } from "./operations";

function createEventFromOperation<TOperation extends Operations.OperationInstance<Operations.OperationType, any>>(
  operation: TOperation
) {
  const parentTypes = [] as Operations.OperationType[];

  let parent = operation.parent;
  while (parent) {
    parentTypes.push(parent.type);
    parent = parent.parent;
  }

  return {
    ouid: operation.uid,
    type: operation.type,
    props: operation.props as any,
    status: operation.state.status,
    error: operation.state.error,
    parentTypes,

    ...(operation.props as any),
  } as OperationEvent<TOperation["type"]>;
}

export function createEventHistoryController() {
  const eventHistory = [] as (null | ReturnType<typeof createEventFromOperation>)[];

  return {
    appendNull() {
      eventHistory.push(null);
    },
    appendEventFromOperationState(operation: Operations.OperationInstance<any, any>) {
      eventHistory.push(createEventFromOperation(operation));
    },
    getEventHistory() {
      return eventHistory;
    },
    clear() {
      eventHistory.length = 0;
    },
  };
}

export type OperationEvent<T extends Operations.OperationType = Operations.OperationType> = Partial<
  typeof createEventFromOperation
> & {
  ouid?: any;
  type: T;
  props?: Operations.OperationPropsPerType[T];
  status?: Operations.OperationStatus;
  error?: Error;
  parentTypes?: Operations.OperationType[];
} & Operations.OperationPropsPerType[T];

type SerializeValues<T> = {
  [K in keyof T]: InstanceToId<T[K]>;
};
export type SerializedOperationEvent<T extends Operations.OperationType = Operations.OperationType> = Partial<
  typeof createEventFromOperation
> & {
  ouid?: any;
  type: T;
  props?: SerializeValues<Operations.OperationPropsPerType[T]>;
  status?: Operations.OperationStatus;
  parentTypes?: Operations.OperationType[];
} & SerializeValues<Operations.OperationPropsPerType[T]>;
