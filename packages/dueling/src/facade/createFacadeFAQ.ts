import { DuelStateContext } from "../DuelStateContext";
import { DuelStateData } from "../data/DuelStateData";
import { DuelStateFacade } from "./DuelStateFacade";
import { createFacadeAsserters } from "./createFacadeAsserters";
import { DuelStateFacadeGetters, createFacadeGetters } from "./createFacadeGetters";

export function createFacadeFAQ(
  facade: DuelStateFacade,
  facadeGetters: DuelStateFacadeGetters = createFacadeGetters(facade),
  cheats: Partial<DuelStateContext["cheats"]> = {}
) {
  const asserters = createFacadeAsserters(facade, facadeGetters, cheats);

  type WrappedAssertFunction<T extends (...args: any[]) => unknown> = (...args: Parameters<T>) => boolean;
  function wrapAssertion<T extends (...args: any[]) => unknown>(assertion: T): WrappedAssertFunction<T> {
    return ((...args: Parameters<T>) => {
      try {
        // @ts-ignore
        assertion(...args);
        return true;
      } catch (err) {
        return false;
      }
    }) as WrappedAssertFunction<T>;
  }

  const facadeFAQ = Object.entries(asserters).reduce((acc, [key, asserter]) => {
    const methodName = key as keyof typeof asserters;
    const method = wrapAssertion(asserter) as WrappedAssertFunction<typeof asserter>;
    acc[methodName] = method;
    return acc;
    // }, {} as Record<keyof typeof asserters, WrappedAssertFunction<(typeof asserters)[keyof typeof asserters]>>);
  }, {} as { [K in keyof typeof asserters]: WrappedAssertFunction<(typeof asserters)[K]> });

  return facadeFAQ;
}

export type DuelStateFacadeFAQ = ReturnType<typeof createFacadeFAQ>;
