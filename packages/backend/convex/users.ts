import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Auth } from "convex/server";

// Helper function to get Clerk user ID
export const getUserId = async (ctx: { auth: Auth }) => {
  return (await ctx.auth.getUserIdentity())?.subject;
};

export const getCurrentUser = query({
  args: {},
  returns: v.union(v.any(), v.null()),
  handler: async (ctx) => {
    const authUserId = await getUserId(ctx);
    if (!authUserId) return null;

    const user = await ctx.db
      .query("users")
      .withIndex("by_auth_id", (q) => q.eq("authId", authUserId))
      .unique();

    return user;
  },
});

export const createUser = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    image: v.optional(v.string()),
    emailVerified: v.optional(v.string()),
    authId: v.string(),
    bio: v.optional(v.string()),
    decks: v.optional(v.string()),
  },
  returns: v.id("users"),
  handler: async (ctx, args) => {
    const userId = await ctx.db.insert("users", {
      name: args.name,
      email: args.email,
      image: args.image,
      emailVerified: args.emailVerified,
      authId: args.authId,
      bio: args.bio || "",
      decks: args.decks || "[]",
    });
    return userId;
  },
});

export const ensureUser = mutation({
  args: {},
  returns: v.union(v.id("users"), v.null()),
  handler: async (ctx) => {
    const authUserId = await getUserId(ctx);
    if (!authUserId) return null;

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_auth_id", (q) => q.eq("authId", authUserId))
      .unique();

    if (existingUser) {
      return existingUser._id;
    }

    // Get user info from Clerk
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    // Extract info from Clerk OAuth provider data
    const name = identity.name || identity.givenName || "User";
    const email = identity.email || "";
    const image = identity.pictureUrl;
    const emailVerified = identity.emailVerified ? new Date().toISOString() : undefined;

    const userId = await ctx.db.insert("users", {
      name,
      email,
      image,
      emailVerified,
      authId: authUserId,
      bio: "",
      decks: "[]",
    });

    return userId;
  },
}); 