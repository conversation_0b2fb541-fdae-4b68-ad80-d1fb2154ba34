import { DuelStateData } from "./DuelStateData";
import { LocationId } from "./LocationId";
import { PlayerNumber, CardInstanceId } from "./primitives";

export function createDataMutators(data: DuelStateData.FullState) {
  let nextCardInstanceId = Object.keys(data.cards).length;
  return {
    //// Cards
    createCard(
      model: DuelStateData.CardModel,
      locationId: LocationId,
      indexAtLocation: number = 0,
      faceUp: boolean = false,
      ownerPlayerNum: PlayerNumber = Number(locationId[0]) as PlayerNumber
    ) {
      const instanceId = ++nextCardInstanceId as CardInstanceId;
      const card = {
        instanceId: instanceId,
        model: model,
        ownerPlayerNum: ownerPlayerNum,
        locationId: locationId,
        prevLocationId: locationId,
        faceUp: faceUp,
      };
      data.cards[instanceId] = card;
      data.locations[locationId].cards.splice(indexAtLocation, 0, instanceId);

      return instanceId;
    },
    moveCardToLocation(instanceId: CardInstanceId, locationId: LocationId, atIndex: number = 0) {
      const card = data.cards[instanceId];
      if (!card) throw new Error(`Card not found: ${instanceId}`);
      if (card.locationId === locationId) throw new Error(`Card already at location: ${locationId}`);

      const currentLocation = data.locations[card.locationId];
      if (!currentLocation) throw new Error(`Current location not found: ${card.locationId}`);

      const targetLocation = data.locations[locationId];
      if (!targetLocation) throw new Error(`Target location not found: ${locationId}`);

      card.prevLocationId = card.locationId;
      const currentIndexAtLocation = currentLocation.cards.indexOf(instanceId);
      currentLocation.cards.splice(currentIndexAtLocation, 1);

      card.locationId = locationId;
      targetLocation.cards.splice(atIndex, 0, instanceId);
    },
    setCardIndexAtLocation(instanceId: CardInstanceId, indexAtLocation: number) {
      const card = data.cards[instanceId];
      if (!card) throw new Error(`Card not found: ${instanceId}`);

      const currentIndexAtLocation = data.locations[card.locationId].cards.indexOf(instanceId);
      data.locations[card.locationId].cards.splice(currentIndexAtLocation, 1);
      data.locations[card.locationId].cards.splice(indexAtLocation, 0, instanceId);
    },
    changeCardFaceUp(instanceId: CardInstanceId, faceUp: boolean) {
      const card = data.cards[instanceId];
      if (!card) throw new Error(`Card not found: ${instanceId}`);
      card.faceUp = faceUp;
    },

    //// Modifiers
    addModifier(modifier: DuelStateData.ActiveModifier) {
      data.activeModifiers.push(modifier);
      return modifier;
    },
    removeModifier(modifier: DuelStateData.ActiveModifier) {
      const index = data.activeModifiers.indexOf(modifier);
      if (index === -1) throw new Error("Modifier not found");
      data.activeModifiers.splice(index, 1);
    },

    //// Players
    changePlayerHealthPoints(playerNumber: PlayerNumber, healthPoints: number) {
      const player = data.players[playerNumber];
      if (!player) throw new Error(`Player not found: ${playerNumber}`);
      player.healthPoints = healthPoints;
    },
    changePlayerActionPoints(playerNumber: PlayerNumber, actionPoints: number) {
      const player = data.players[playerNumber];
      if (!player) throw new Error(`Player not found: ${playerNumber}`);
      player.actionPoints = actionPoints;
    },
    changePlayerActionPointsCap(playerNumber: PlayerNumber, actionPointsCap: number) {
      const player = data.players[playerNumber];
      if (!player) throw new Error(`Player not found: ${playerNumber}`);
      player.actionPointsCap = actionPointsCap;
    },

    //// Core
    changeDuelPhase(phase: DuelStateData.FullState["phase"]) {
      data.phase = phase;
    },

    //// Turn
    changeTurnPlayer(playerNumber: PlayerNumber | null) {
      data.turn.playerNumber = playerNumber;
    },
    incrementTurnNumber() {
      data.turn.turnNumber++;
    },

    rememberUnitActionThisTurn(card: CardInstanceId, action: DuelStateData.UnitActionType) {
      if (!data.turn.actionsMade[card]) {
        data.turn.actionsMade[card] = [];
      }
      data.turn.actionsMade[card].push(action);
    },
    forgetAllUnitActionsThisTurn() {
      for (const key in data.turn.actionsMade) {
        const cardInstanceId = Number(key) as CardInstanceId;
        delete data.turn.actionsMade[cardInstanceId];
      }
    },
    forgetUnitActionsThisTurn(card: CardInstanceId) {
      delete data.turn.actionsMade[card];
    },
    forgetUnitActionsThisTurnByType(card: CardInstanceId, ...types: DuelStateData.UnitActionType[]) {
      if (data.turn.actionsMade[card]) {
        data.turn.actionsMade[card] = data.turn.actionsMade[card].filter(action => !types.includes(action));
        if (data.turn.actionsMade[card].length === 0) {
          delete data.turn.actionsMade[card];
        }
      }
    },
  };
}
