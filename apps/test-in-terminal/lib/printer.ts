/// <reference types="node" />

import chalk from 'chalk';
import readline from 'readline';

import { DuelStateData } from '@drimgar/dueling/src/data/DuelStateData';

import {
  LANES_COUNT,
  LocationId,
  makeAllPossibleLocationIds,
} from '@drimgar/dueling/src/data/LocationId';
import { DuelStateFacade } from '@drimgar/dueling/src/facade/DuelStateFacade';
import { createEventHistoryController } from '@drimgar/dueling/src/gameplay/operation-events';
import { serializeOperationProperties } from '@drimgar/dueling/src/serialization/serializeOperationProperties';

function stringify(obj: Record<string, any>): string {
  return Object.entries(obj)
    .map(([key, value]) => `${key} ${value}`)
    .join(', ');
}

export module DuelPrinter {
  const defaultSettings = {
    hideFaceDownCards: true,
    condensedDeck: true,
  };

  export type Settings = typeof defaultSettings;

  export function printDuelStateOnScreen(
    data: DuelStateData.FullState,
    customSettings?: Partial<typeof defaultSettings>
  ) {
    const settings = { ...defaultSettings, ...customSettings };

    const facade = DuelStateFacade.createFromData(data);

    const allLocationIds = makeAllPossibleLocationIds(LANES_COUNT);
    const locationPrintLinesMap = {
      Hand: allLocationIds.filter(LocationId.isHand),
      Deck: allLocationIds.filter(LocationId.isDeck),
      Grave: allLocationIds.filter(LocationId.isGraveyard),
      Units: allLocationIds.filter(LocationId.isUnitField),
      Traps: allLocationIds.filter(LocationId.isTrapField),
      Abyss: allLocationIds.filter(LocationId.isAbyss),
    } as const;

    function renderDuelStateToString(data: DuelStateData.FullState) {
      function drawPlayerBar(player: DuelStateData.Player) {
        const num = player.playerNumber,
          hp = player.healthPoints,
          ap = player.actionPoints,
          apc = player.actionPointsCap;

        const apStarsFill = ap > 0 ? chalk.bold(chalk.green('★ ')).repeat(ap) : '';
        const apStarsEmpty = apc > ap ? chalk.gray('☆ ').repeat(apc - ap) : '';
        const apStars = `${apStarsFill}${apStarsEmpty}`;

        const playerTitle = `Player ${num}`;
        const playerTitleFormatted =
          facade.turn.playerNumber === num ? chalk.bold(playerTitle) : playerTitle;

        const detailStrings = [
          playerTitleFormatted,
          `♥ ${chalk.bold(chalk.green(String(hp).padStart(2, ' ')))}`,
          `AP ${apStars}`,
        ];

        return chalk.cyan(detailStrings.join(chalk.blue('  |  ')));
      }

      function drawCard($card: DuelStateData.CardInstance) {
        const card = facade.cards[$card.instanceId];
        const colorFn = card.isTrap ? chalk.magenta : card.stars ? chalk.white : chalk.yellow;
        const content =
          card.isFaceDown && settings.hideFaceDownCards
            ? '  '
            : card.isTrap
            ? 'T☠'
            : String(card.power).padStart(2, ' ');
        const frame = card.isUnit && card.model.effects.length ? '{}' : '[]';
        const result = `${frame[0]}${content}${frame[1]}`;
        return colorFn(result);
      }

      function drawEmptyField() {
        return chalk.gray('[  ]');
      }

      let lines = [] as string[];
      for (let player = 1; player <= 2; player++) {
        let playerResult = {
          Units: Array(LANES_COUNT).fill(drawEmptyField()),
          Traps: Array(LANES_COUNT).fill(drawEmptyField()),
          Abyss: [] as string[],
          Hand: [] as string[],
          Grave: [] as string[],
          Deck: [] as string[],
        };

        for (const key in locationPrintLinesMap) {
          const locationName = key as keyof typeof locationPrintLinesMap;
          for (const locationId of locationPrintLinesMap[locationName]) {
            if (LocationId.extractPlayerNum(locationId) !== player) continue;

            const locationCards = data.locations[locationId].cards.toReversed();
            if (LocationId.isField(locationId)) {
              const position = LocationId.extractFieldNumber(locationId) - 1;
              playerResult[locationName][position] = locationCards.length
                ? drawCard(data.cards[locationCards[0]])
                : drawEmptyField();
            } else if (locationName !== 'Hand' && settings.condensedDeck) {
              for (let i = 0; i < locationCards.length; i++) {
                const card = data.cards[locationCards[i]];
                const cardPrint = drawCard(card);
                playerResult[locationName].push(i === 0 ? cardPrint : chalk.yellow(']'));
              }
            } else {
              for (const cardId of locationCards) {
                const card = data.cards[cardId];
                playerResult[locationName].push(drawCard(card));
              }
            }
          }
        }

        const locationNames = Object.keys(playerResult) as (keyof typeof playerResult)[];
        if (player === 1) locationNames.reverse();
        for (const locationName of locationNames) {
          // if (key === "Abyss") continue;
          lines.push(
            `  ${chalk.blue(locationName.padEnd(6))} ${playerResult[locationName].join('')}`
          );
        }

        lines.push(``);
      }

      lines.reverse();

      return `${drawPlayerBar(facade.players[2])}\n${lines.join('\n')}\n\n${drawPlayerBar(
        facade.players[1]
      )}`;
    }

    console.log(renderDuelStateToString(data));
  }

  export function printDuelEventsOnScreen(events: ReturnType<typeof createEventHistoryController>) {
    function prettifyStatus(status: string) {
      switch (status.toLowerCase()) {
        case 'began':
          return '·';
        case 'ended':
          return '●';
        case 'failed':
          return '✖';
        default:
          return status;
      }
    }

    const maxEventsToPrint = process.stdout.rows ? process.stdout.rows - 50 : 40; // subtract 1 for the console.table header
    const eventsToPrint = events.getEventHistory().toReversed().slice(0, maxEventsToPrint);
    console.table(
      eventsToPrint.map(e =>
        e === null
          ? { uid: '', type: '', status: '', props: '' }
          : {
              type: e.type,
              status: prettifyStatus(e.status?.toUpperCase() ?? ''),
              props: stringify(serializeOperationProperties(e.props as any)),
              uid: e.ouid,
            }
      )
    );
  }

  export function printErrorsOnScreen(errors: (Error | string)[]) {
    if (errors.length < 1) {
      console.log();
    } else {
      const lines = errors.map(e => (typeof e === 'string' ? e : e.message));
      // console.log(chalk.red(`Errors: ${chalk.bold(lines.join(" | "))}`));
      console.log(chalk.red(lines.map(ln => `Error: ${chalk.bold(ln)}`).join('\n')));
    }
  }

  export function pauseUntilKeypress(): Promise<string> {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');

    return new Promise<string>(resolve => {
      const keypressHandler = (chunk: any, key: any) => {
        process.stdin.pause();
        process.stdin.setRawMode(false);
        process.stdin.removeListener('keypress', keypressHandler);
        rl.close();

        if (key && key.ctrl && key.name === 'c') {
          process.exit();
        } else {
          resolve(key ? key.name : chunk);
        }
      };

      process.stdin.on('keypress', keypressHandler);
    });
  }

  export function pauseUntilInput(): Promise<string> {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise<string>(resolve =>
      rl.question('Press any key to continue...', k => {
        rl.close();
        resolve(k);
      })
    );
  }

  export function pause(duration: number) {
    const start = Date.now();
    while (Date.now() - start < duration) {}
  }
}
